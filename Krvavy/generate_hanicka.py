#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from openai import OpenAI
import requests

# Set your OpenAI API key
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

def generate_hanicka_image():
    """Generate Hanička bez rúk image with careful prompt"""
    
    if not client.api_key:
        print("❌ Please set your OpenAI API key as environment variable:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        return
    
    # Careful prompt for <PERSON><PERSON><PERSON><PERSON> bez rúk - focusing on fairy tale elements
    prompt = """Stylized flat illustration in retro fairytale horror aesthetic, inspired by Eastern European folk tales.
The scene shows a young girl's silhouette in a long dress standing in a cottage doorway, depicted in a melancholic fairy tale style.
The figure is shown from behind or in profile, with flowing dress and hair, creating a tragic but beautiful composition.
Use simplified geometric shapes and a minimalist composition.
Color palette is limited to parchment beige (#D2B48C), blood red (#750000), dark brown (#2C1A0C), and small golden accents.
Background should have a soft paper texture with a cottage setting.
No gradients, no photorealism – vector-style silhouettes only.
The image should feel mysterious, folkloric, symbolic, and melancholic like a classic fairy tale illustration.
Consistent with podcast episode artwork.
NO TEXT OR LETTERS should appear anywhere in the image."""
    
    try:
        print("🎨 Generating Hanička bez rúk image...")
        print("📡 Sending request to OpenAI...")
        
        response = client.images.generate(
            prompt=prompt,
            n=1,
            size="1024x1024",
            model="dall-e-3"
        )
        
        print("📡 Response received!")
        
        image_url = response.data[0].url
        print(f"✅ Image generated successfully!")
        print(f"🔗 URL: {image_url}")
        
        # Download the image
        output_dir = os.path.expanduser("~/Desktop/Nové_Obrázky_Príbehy")
        os.makedirs(output_dir, exist_ok=True)
        
        image_response = requests.get(image_url)
        if image_response.status_code == 200:
            filepath = os.path.join(output_dir, "Hanicka_bez_ruk.png")
            with open(filepath, 'wb') as f:
                f.write(image_response.content)
            print(f"✅ Hanička bez rúk image saved: {filepath}")
            return filepath
        else:
            print(f"❌ Failed to download Hanička bez rúk image")
            return None
            
    except Exception as e:
        print(f"❌ Error generating Hanička bez rúk image: {str(e)}")
        return None

if __name__ == "__main__":
    generate_hanicka_image()
