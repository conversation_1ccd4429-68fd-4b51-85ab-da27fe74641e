// Critical debugging and fallback script
// This file is in /public so it won't be processed by Vite

console.log('🔪 Debug script loaded - <PERSON><PERSON><PERSON><PERSON>');

// Global error handlers
window.addEventListener('error', function(e) {
  console.error('🚨 Global JavaScript error:', e.error, e.filename, e.lineno);
  showFallbackContent('JavaScript Error: ' + e.message + ' at ' + e.filename + ':' + e.lineno);
});

window.addEventListener('unhandledrejection', function(e) {
  console.error('🚨 Unhandled promise rejection:', e.reason);
  showFallbackContent('Promise Error: ' + e.reason);
});

// Fallback content display function
function showFallbackContent(errorMsg) {
  console.log('🔄 Showing fallback content due to:', errorMsg);
  
  // Hide loading screen
  const loadingScreen = document.getElementById('loading-screen');
  if (loadingScreen) {
    loadingScreen.style.display = 'none';
    console.log('✅ Loading screen hidden');
  }

  // Show fallback content
  const fallback = document.getElementById('fallback-content');
  if (fallback) {
    fallback.style.display = 'block';
    console.log('✅ Fallback content shown');
    
    const debugInfo = document.getElementById('debug-info');
    if (debugInfo) {
      debugInfo.innerHTML = `<p style="color: #ff6b6b;">Debug: ${errorMsg}</p><p style="color: #DAA520;">Time: ${new Date().toLocaleTimeString()}</p>`;
    }
  }
}

// Set a timeout to show fallback if Vue doesn't load
let vueLoadTimeout = setTimeout(() => {
  console.log('⏰ Vue.js loading timeout (5 seconds) - showing fallback');
  showFallbackContent('Vue.js loading timeout after 5 seconds');
}, 5000);

// Function to clear timeout when Vue loads successfully
window.clearVueTimeout = function() {
  if (vueLoadTimeout) {
    clearTimeout(vueLoadTimeout);
    vueLoadTimeout = null;
    console.log('✅ Vue timeout cleared - app loaded successfully');
  }
};

// Immediate fallback test - show fallback after 2 seconds if nothing happens
setTimeout(() => {
  const app = document.getElementById('app');
  const loadingScreen = document.getElementById('loading-screen');
  
  // Check if Vue has mounted anything
  if (app && app.children.length <= 1 && loadingScreen && loadingScreen.style.display !== 'none') {
    console.log('⚠️ No Vue content detected after 2 seconds, showing fallback');
    showFallbackContent('No Vue content detected after 2 seconds');
  }
}, 2000);

// Force fallback after 1 second for immediate testing
setTimeout(() => {
  console.log('🔧 FORCE FALLBACK FOR TESTING - 1 second timeout');
  showFallbackContent('FORCED FALLBACK FOR TESTING');
}, 1000);

console.log('🔪 Debug script setup complete - waiting for Vue.js...');
