#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from openai import OpenAI
import requests

# Set your OpenAI API key
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

def test_single_image():
    """Test generating a single image"""
    
    if not client.api_key:
        print("❌ Please set your OpenAI API key as environment variable:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        return
    
    # Test prompt
    prompt = """Stylized flat illustration in retro fairytale horror aesthetic. Limited color palette: parchment beige, blood red, dark brown, golden accents. Simplified geometric shapes, minimalist composition. Eastern European folklore style. Vector-style silhouettes only. NO TEXT.

    Scene: Geometric ziggurat in dark brown, clock showing 3:33 in gold, symbols in blood red, parchment background."""
    
    try:
        print("🎨 Testing image generation...")
        print(f"Prompt: {prompt[:100]}...")
        print("📡 Sending request to OpenAI...")

        response = client.images.generate(
            prompt=prompt,
            n=1,
            size="1024x1024",
            model="dall-e-3"
        )

        print("📡 Response received!")
        
        image_url = response.data[0].url
        print(f"✅ Image generated successfully!")
        print(f"🔗 URL: {image_url}")
        
        # Download the image
        output_dir = os.path.expanduser("~/Desktop/Nové_Obrázky_Príbehy")
        os.makedirs(output_dir, exist_ok=True)
        
        image_response = requests.get(image_url)
        if image_response.status_code == 200:
            filepath = os.path.join(output_dir, "test_babylon_333.png")
            with open(filepath, 'wb') as f:
                f.write(image_response.content)
            print(f"✅ Test image saved: {filepath}")
        else:
            print(f"❌ Failed to download test image")
            
    except Exception as e:
        print(f"❌ Error generating test image: {str(e)}")

if __name__ == "__main__":
    test_single_image()
