<!DOCTYPE html>
<html lang="sk">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
  <meta name="theme-color" content="#2C1A0C" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  <meta name="apple-mobile-web-app-title" content="Krvavý Dobšinský" />
  
  <!-- App Icons -->
  <link rel="icon" type="image/png" sizes="32x32" href="/icons/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/icons/favicon-16x16.png">
  <link rel="apple-touch-icon" sizes="180x180" href="/icons/apple-touch-icon.png">
  <link rel="mask-icon" href="/icons/safari-pinned-tab.svg" color="#750000">
  
  <!-- PWA Manifest -->
  <link rel="manifest" href="/manifest.json">
  
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://anchor.fm">
  <link rel="preconnect" href="https://d3t3ozftmdmh3i.cloudfront.net">
  <link rel="preconnect" href="https://raw.githubusercontent.com">
  
  <title>Krvavý Dobšinský - Horror Podcast</title>
  
  <style>
    /* Loading screen styles - HIDDEN BY DEFAULT */
    #loading-screen {
      display: none;
    }
    
    .loading-logo {
      width: 120px;
      height: 120px;
      margin-bottom: 20px;
      border-radius: 20px;
      background: #750000;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 48px;
      font-weight: bold;
      color: #D2B48C;
      box-shadow: 0 8px 32px rgba(117, 0, 0, 0.3);
    }
    
    .loading-text {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 10px;
      text-align: center;
    }
    
    .loading-subtitle {
      font-size: 14px;
      opacity: 0.7;
      text-align: center;
      margin-bottom: 30px;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(210, 180, 140, 0.3);
      border-top: 3px solid #750000;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #2C1A0C;
      color: #D2B48C;
      overflow-x: hidden;
    }
    
    #app {
      min-height: 100vh;
    }
  </style>
  <script type="module" crossorigin src="/assets/main-mxOmiw7u.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/main-D-sgq-w0.css">
</head>
<body>
  <div id="loading-screen">
    <div class="loading-logo">🔪</div>
    <div class="loading-text">Krvavý Dobšinský</div>
    <div class="loading-subtitle">Načítava sa...</div>
    <div class="loading-spinner"></div>
  </div>
  
  <div id="app">
    <!-- Fallback content if Vue doesn't load -->
    <div id="fallback-content" style="display: block; padding: 20px; text-align: center; color: #D2B48C; background: #2C1A0C; min-height: 100vh; font-family: -apple-system, BlinkMacSystemFont, sans-serif;">
      <div style="margin-bottom: 30px;">
        <div style="width: 80px; height: 80px; background: #750000; border-radius: 16px; display: inline-flex; align-items: center; justify-content: center; font-size: 40px; margin-bottom: 20px;">🔪</div>
        <h1 style="margin: 0; font-size: 28px; color: #D2B48C;">Krvavý Dobšinský</h1>
        <p style="margin: 10px 0; color: #DAA520; opacity: 0.8;">Horror Podcast - Fallback Mode</p>
      </div>

      <div style="max-width: 400px; margin: 0 auto;">
        <div style="background: rgba(117, 0, 0, 0.2); border: 1px solid #750000; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
          <h3 style="margin: 0 0 10px 0; color: #D2B48C;">🎧 Test Epizódy</h3>
          <div style="text-align: left;">
            <div style="padding: 10px; margin: 5px 0; background: rgba(44, 26, 12, 0.4); border-radius: 8px; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(117, 0, 0, 0.3)'" onmouseout="this.style.background='rgba(44, 26, 12, 0.4)'">
              <strong>🔪 Krvavá Mária</strong><br>
              <small style="color: #DAA520;">Testovacia epizóda • 10:00</small>
            </div>
            <div style="padding: 10px; margin: 5px 0; background: rgba(44, 26, 12, 0.4); border-radius: 8px; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(117, 0, 0, 0.3)'" onmouseout="this.style.background='rgba(44, 26, 12, 0.4)'">
              <strong>🏰 Strašidelný Zámok</strong><br>
              <small style="color: #DAA520;">Demo epizóda • 15:00</small>
            </div>
            <div style="padding: 10px; margin: 5px 0; background: rgba(44, 26, 12, 0.4); border-radius: 8px; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(117, 0, 0, 0.3)'" onmouseout="this.style.background='rgba(44, 26, 12, 0.4)'">
              <strong>🧛 Upírska Legenda</strong><br>
              <small style="color: #DAA520;">Ukážka • 12:30</small>
            </div>
          </div>
        </div>

        <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
          <button style="background: #750000; color: #D2B48C; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.3s ease;" onmouseover="this.style.background='#8B0000'" onmouseout="this.style.background='#750000'">
            🏠 Podcast
          </button>
          <button style="background: #3d2817; color: #D2B48C; border: 1px solid #DAA520; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.3s ease;" onmouseover="this.style.background='#4d3217'" onmouseout="this.style.background='#3d2817'">
            ❤️ Uložené
          </button>
          <button style="background: #3d2817; color: #D2B48C; border: 1px solid #DAA520; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.3s ease;" onmouseover="this.style.background='#4d3217'" onmouseout="this.style.background='#3d2817'">
            ℹ️ O podcaste
          </button>
        </div>

        <div style="margin-top: 20px; font-size: 12px; color: #DAA520; text-align: center;">
          <p>✅ Aplikácia funguje v fallback režime</p>
          <p>📱 iOS WebView načítaný úspešne</p>
          <p>🔧 Čas: <span id="current-time"></span></p>
        </div>
      </div>
    </div>
  </div>

  <!-- JavaScript removed - using pure HTML/CSS solution -->

</body>
</html>
