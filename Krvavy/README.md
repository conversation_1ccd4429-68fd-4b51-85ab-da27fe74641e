# 🔪 Krvavý Dobšinský - iOS App

**Slovenský horror podcast v natívnej iOS aplikácii**

Táto aplikácia prináša populárny slovenský horror podcast **Krvavý Dobšinský** priamo do vášho iPhone alebo iPad. Vytvorená pomocou moderných technológií Capacitor, Vue 3 a TypeScript.

## 📱 Funkcie

- **🎧 Streamovanie epizód** - Prehrávanie priamo z RSS feedu
- **❤️ Uložené epizódy** - Označte si obľúbené epizódy
- **📥 Offline prehrávanie** - Stiahnite si epizódy na offline počúvanie
- **🔍 Vyhľadávanie** - Nájdite konkrétne epizódy
- **📤 Zdieľanie** - Zdieľajte epizódy s priateľmi
- **🌙 Tmavý režim** - Optimalizované pre horror atmosféru
- **🎨 Retro design** - Inšpirovaný východoslovenským folklórom

## 🛠 Technológie

- **Frontend**: Vue 3 + TypeScript + Vite
- **Mobile**: Capacitor (iOS)
- **State Management**: Pinia
- **Styling**: Custom CSS s retro horror témou
- **Audio**: HTML5 Audio API + Capacitor Media plugins
- **Storage**: Capacitor Preferences API

## 🚀 Rýchly štart

### Predpoklady

- **Node.js 18+** - [Stiahnuť](https://nodejs.org/)
- **Xcode 14+** - [Mac App Store](https://apps.apple.com/app/xcode/id497799835)
- **iOS Simulator** alebo fyzické iOS zariadenie
- **Apple Developer Account** (pre distribúciu)

### Inštalácia

1. **Klonujte repozitár**
   ```bash
   git clone https://github.com/vldseman/krvavyapp.git
   cd krvavyapp
   ```

2. **Spustite setup skript**
   ```bash
   chmod +x scripts/setup-project.sh
   ./scripts/setup-project.sh
   ```

3. **Spustite vývojový server**
   ```bash
   npm run dev
   ```

### iOS Build

1. **Pridajte iOS platformu**
   ```bash
   npm run cap:add
   ```

2. **Buildnite pre iOS**
   ```bash
   npm run cap:build
   ```

3. **Otvorte v Xcode**
   ```bash
   npm run cap:open
   ```

## 📋 Dostupné príkazy

```bash
# Vývoj
npm run dev              # Spustí dev server
npm run build            # Build pre produkciu
npm run preview          # Preview produkčného buildu

# Capacitor
npm run cap:add          # Pridá iOS platformu
npm run cap:sync         # Synchronizuje zmeny
npm run cap:build        # Build + sync + open Xcode
npm run cap:open         # Otvorí v Xcode
npm run cap:run          # Build a spustí na zariadení

# Utility
npm run type-check       # TypeScript kontrola
npm run lint             # ESLint kontrola
```

## 🏗 Štruktúra projektu

```
Krvavy/
├── src/
│   ├── components/          # Vue komponenty
│   │   ├── AudioPlayer.vue  # Audio prehrávač
│   │   ├── EpisodeCard.vue  # Karta epizódy
│   │   └── BottomNavigation.vue
│   ├── views/              # Stránky aplikácie
│   │   ├── Home.vue        # Hlavná stránka
│   │   ├── Episode.vue     # Detail epizódy
│   │   ├── Saved.vue       # Uložené epizódy
│   │   └── About.vue       # O podcaste
│   ├── stores/             # Pinia stores
│   │   └── podcast.ts      # Podcast state management
│   ├── router/             # Vue Router
│   ├── types/              # TypeScript definície
│   └── style.css           # Globálne štýly
├── ios/                    # iOS Capacitor projekt
├── public/                 # Statické súbory
│   ├── icons/              # App ikony
│   └── manifest.json       # PWA manifest
├── scripts/                # Build skripty
└── podcast_images/         # Obrázky epizód
```

## 🎨 Design systém

### Farebná paleta
- **Parchment Beige**: `#D2B48C` - Hlavný text
- **Blood Red**: `#750000` - Akcenty a tlačidlá
- **Dark Brown**: `#2C1A0C` - Pozadie
- **Golden**: `#DAA520` - Zvýraznenia
- **Dark Gray**: `#1a0f06` - Sekundárne pozadie

### Typografia
- **Font**: System fonts (-apple-system, BlinkMacSystemFont)
- **Štýl**: Retro horror s východoslovenským folklórnym nádychom

## 📱 iOS Funkcie

- **Background Audio** - Prehrávanie na pozadí
- **Lock Screen Controls** - Ovládanie z uzamknutej obrazovky
- **AirPlay Support** - Streamovanie na Apple TV/HomePod
- **CarPlay Ready** - Príprava na CarPlay integráciu
- **Siri Shortcuts** - Hlasové ovládanie (budúca funkcia)
- **Widget Support** - iOS widgety (budúca funkcia)

## 📻 O podcaste

**Krvavý Dobšinský** je týždenný podcast tých najpríšernejších príbehov, povier a strašidiel. Tradičné slovenské hororové poviedky a príbehy inšpirované východoslovenským folklórom.

### RSS Feed
```
https://anchor.fm/s/8db2e1ec/podcast/rss
```

## 🚀 Distribúcia

### TestFlight (Beta)
1. Archive aplikáciu v Xcode
2. Upload do App Store Connect
3. Vytvorte TestFlight build
4. Pozvite beta testerov

### App Store
1. Pripravte metadata (popis, screenshoty, ikony)
2. Nastavte ceny a dostupnosť
3. Odošlite na review
4. Po schválení publikujte

## 🤝 Prispievanie

1. Fork repozitár
2. Vytvorte feature branch (`git checkout -b feature/nova-funkcia`)
3. Commit zmeny (`git commit -am 'Pridaj novú funkciu'`)
4. Push do branch (`git push origin feature/nova-funkcia`)
5. Vytvorte Pull Request

## 📧 Kontakt

- **Email**: [<EMAIL>](mailto:<EMAIL>)
- **Instagram**: [@krvavydobsinsky](https://www.instagram.com/krvavydobsinsky/)
- **Podpora**: [HeroHero](https://herohero.co/krvavydobsinsky)

## 📄 Licencia

MIT License - pozrite [LICENSE](LICENSE) súbor pre detaily.

## 🙏 Poďakovanie

- **Vladimír Seman** - Autor podcastu a aplikácie
- **Vue.js tím** - Za úžasný framework
- **Capacitor tím** - Za cross-platform riešenie
- **Všetkým posluchačom** - Za podporu podcastu

---

**Vytvorené s ❤️ pre slovenský horror podcast komunitu**


