{"name": "@capacitor/cli", "version": "5.7.8", "description": "Capacitor: Cross-platform apps with JavaScript and the web", "homepage": "https://capacitorjs.com", "author": "Ionic Team <<EMAIL>> (https://ionic.io)", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ionic-team/capacitor.git"}, "bugs": {"url": "https://github.com/ionic-team/capacitor/issues"}, "files": ["assets/", "bin/", "dist/**/*.js", "dist/declarations.d.ts"], "keywords": ["ionic", "ionic framework", "capacitor", "universal app", "progressive web apps", "cross platform"], "engines": {"node": ">=16.0.0"}, "main": "dist/index.js", "types": "dist/declarations.d.ts", "bin": {"capacitor": "./bin/capacitor", "cap": "./bin/capacitor"}, "scripts": {"build": "npm run clean && npm run assets && tsc", "clean": "rimraf ./dist", "assets": "node ../scripts/pack-cli-assets.mjs", "prepublishOnly": "npm run build", "test": "jest -i", "watch": "npm run assets && tsc -w"}, "dependencies": {"@ionic/cli-framework-output": "^2.2.5", "@ionic/utils-fs": "^3.1.6", "@ionic/utils-subprocess": "^2.1.11", "@ionic/utils-terminal": "^2.3.3", "commander": "^9.3.0", "debug": "^4.3.4", "env-paths": "^2.2.0", "kleur": "^4.1.4", "native-run": "^2.0.0", "open": "^8.4.0", "plist": "^3.0.5", "prompts": "^2.4.2", "rimraf": "^4.4.1", "semver": "^7.3.7", "tar": "^6.1.11", "tslib": "^2.4.0", "xml2js": "^0.5.0"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.0", "@types/plist": "^3.0.2", "@types/prompts": "^2.0.14", "@types/semver": "^7.3.10", "@types/tar": "^6.1.1", "@types/tmp": "^0.2.3", "@types/xml2js": "0.4.5", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-jasmine2": "^29.5.0", "tmp": "^0.2.1", "ts-jest": "^29.0.5", "typescript": "~5.0.2"}, "jest": {"preset": "ts-jest", "testRunner": "jest-jasmine2"}, "publishConfig": {"access": "public"}}