#import <UIKit/UIKit.h>

//! Project version number for CapacitorCordova.
FOUNDATION_EXPORT double CapacitorCordovaVersionNumber;

//! Project version string for CapacitorCordova.
FOUNDATION_EXPORT const unsigned char CapacitorCordovaVersionString[];

#import <Cordova/AppDelegate.h>
#import <Cordova/CDV.h>
#import <Cordova/CDVAvailability.h>
#import <Cordova/CDVCommandDelegate.h>
#import <Cordova/CDVCommandDelegateImpl.h>
#import <Cordova/CDVConfigParser.h>
#import <Cordova/CDVInvokedUrlCommand.h>
#import <Cordova/CDVPlugin+Resources.h>
#import <Cordova/CDVPlugin.h>
#import <Cordova/CDVPluginManager.h>
#import <Cordova/CDVPluginResult.h>
#import <Cordova/CDVScreenOrientationDelegate.h>
#import <Cordova/CDVURLProtocol.h>
#import <Cordova/CDVViewController.h>
#import <Cordova/CDVWebViewProcessPoolFactory.h>
#import <Cordova/NSDictionary+CordovaPreferences.h>
