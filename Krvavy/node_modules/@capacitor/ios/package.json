{"name": "@capacitor/ios", "version": "5.7.8", "description": "Capacitor: Cross-platform apps with JavaScript and the web", "homepage": "https://capacitorjs.com", "author": "Ionic Team <<EMAIL>> (https://ionic.io)", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ionic-team/capacitor.git"}, "bugs": {"url": "https://github.com/ionic-team/capacitor/issues"}, "files": ["Capacitor/Capacitor/", "CapacitorCordova/CapacitorCordova/", "Capacitor.podspec", "CapacitorCordova.podspec", "scripts/pods_helpers.rb"], "scripts": {"verify": "npm run xc:build:Capacitor && npm run xc:build:CapacitorCordova", "xc:build:Capacitor": "cd Capacitor && xcodebuild -workspace Capacitor.xcworkspace -scheme Capacitor && cd ..", "xc:build:CapacitorCordova": "cd CapacitorCordova && xcodebuild && cd .."}, "peerDependencies": {"@capacitor/core": "^5.7.0"}, "publishConfig": {"access": "public"}}