#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
import subprocess
from pathlib import Path

def create_git_repo_structure():
    """Create git repository structure for podcast images"""
    
    # Create images directory in git repo
    images_dir = "podcast_images"
    os.makedirs(images_dir, exist_ok=True)
    
    # Source directory with generated images
    source_dir = os.path.expanduser("~/Desktop/Nové_Obrázky_Príbehy")
    
    # Mapping of generated images to RSS episode names
    # Based on RSS feed analysis - matching existing episodes
    image_mappings = {
        # Exact matches from RSS
        "Babylon_333.png": "babylon-333.png",
        "Newportský_duch.png": "newportsky-duch.png", 
        "Perníková_chalúpka.png": "pernikova-chalupka.png",
        "Bezruká_Príbeh_prekliateho_mlyna.png": "bezruka-pribeh-prekliateho-mlyna.png",
        "Za_starým_dubom.png": "za-starym-dubom.png",
        "Slavošovský_tunel.png": "slavosovsky-tunel.png",
        "Niečo_v_rohu.png": "nieco-v-rohu.png",
        "Noku.png": "noku.png",
        "Upír_fekišovský.png": "upir-fekisovsky.png",
        "Horory_zo_Šíravy.png": "horory-zo-siravy.png",
        "Fotky_smrti.png": "fotky-smrti.png",
        "Návrat_zo_záhrobia.png": "navrat-zo-zahrobia.png",
        "Trebišovské_horory.png": "trebisovske-horory.png",
        "Zmok_(bez_kostola_a_umývania)1.png": "zmok-bez-kostola-a-umyvania-cast-1.png",
        "Zmok_(bez_kostola_a_umývania)2.png": "zmok-bez-kostola-a-umyvania-cast-2.png",
        "Jure_Grando_(1579_-_1656)_u.png": "jure-grando-1579-1656-upir.png",
        "Mužský_narcizmus_-_Vlkolak.png": "muzsky-narcizmus-vlkolak.png",
        "Ako_nám_rozprávky_pomáhajú.png": "ako-nam-rozpravky-pomahaju.png",
        "Piatko_a_Pustaj.png": "piatko-a-pustaj.png",
        "Zakliata_hora.png": "zakliata-hora.png",
        "Mŕtvy_frajer.png": "mrtvy-frajer.png",
        "Démon_v_kapustnom_poli.png": "demon-v-kapustnom-poli.png",
        "Zberačka_jahôd.png": "zberackajahod.png",
        "Môj_otec_bol_už_raz_ženatý.png": "moj-otec-bol-uz-raz-zenaty.png",
        
        # Additional images for future episodes or variants
        "Entita_-_skutočný_príbeh.png": "entita-skutocny-pribeh.png",
        "Tisíc_rytierov.png": "tisic-rytierov.png",
        "Borievka_-_desivá_rozprávka_o.png": "borievka-desiva-rozpravka.png",
        "Slnko_Mesiac_a_Tália_(Šípková_Ruženka).png": "slnko-mesiac-a-talia-sipkova-ruzenka.png",
        "Malý_chlapec.png": "maly-chlapec.png",
        "Príbeh_klaviristu_Abrahama.png": "pribeh-klaviristu-abrahama.png",
        "Škofja_Loka_nacistami_okupované.png": "skofja-loka-nacistami-okupovane.png"
    }
    
    print(f"📁 Creating git repository structure...")
    print(f"📂 Source: {source_dir}")
    print(f"📂 Target: {images_dir}")
    
    copied_count = 0
    missing_count = 0
    
    # Copy and rename images
    for original_name, new_name in image_mappings.items():
        source_path = os.path.join(source_dir, original_name)
        target_path = os.path.join(images_dir, new_name)
        
        if os.path.exists(source_path):
            shutil.copy2(source_path, target_path)
            print(f"✅ {original_name} → {new_name}")
            copied_count += 1
        else:
            print(f"❌ Missing: {original_name}")
            missing_count += 1
    
    print(f"\n📊 Summary:")
    print(f"✅ Copied: {copied_count}")
    print(f"❌ Missing: {missing_count}")
    
    return images_dir, copied_count

def initialize_git_repo(images_dir):
    """Initialize git repository and commit images"""
    
    try:
        # Initialize git repo if not exists
        if not os.path.exists(".git"):
            print("🔧 Initializing git repository...")
            subprocess.run(["git", "init"], check=True)
            
        # Create README
        readme_content = """# Krvavý Dobšinský - Podcast Episode Images

This repository contains artwork for the Krvavý Dobšinský horror podcast episodes.

## Image Style
- Retro fairytale horror aesthetic
- Limited color palette: parchment beige, blood red, dark brown, golden accents
- Simplified geometric shapes and minimalist composition
- Eastern European folklore inspiration
- Vector-style silhouettes and symbolic elements

## Usage
These images are designed for podcast episode artwork and can be used by AI builders to match episodes with their corresponding visual content.

## Episodes Covered
The images correspond to various episodes from the Krvavý Dobšinský podcast, including classic Slovak horror stories, folk tales, and supernatural narratives.

## RSS Feed
https://anchor.fm/s/8db2e1ec/podcast/rss

## Contact
For more information about the podcast: <EMAIL>
"""
        
        with open("README.md", "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        # Add files to git
        print("📝 Adding files to git...")
        subprocess.run(["git", "add", "."], check=True)
        
        # Commit
        print("💾 Committing files...")
        subprocess.run([
            "git", "commit", "-m", 
            "Add podcast episode artwork - retro fairytale horror style"
        ], check=True)
        
        print("✅ Git repository created successfully!")
        
        # Show git status
        print("\n📋 Git status:")
        result = subprocess.run(["git", "status", "--short"], 
                              capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        else:
            print("Working tree clean")
            
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Git error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def generate_urls(images_dir):
    """Generate GitHub URLs for the images"""
    
    # Note: These URLs will work after pushing to GitHub
    base_url = "https://raw.githubusercontent.com/YOUR_USERNAME/YOUR_REPO/main"
    
    print(f"\n🔗 GitHub URLs (after pushing to GitHub):")
    print(f"📝 Replace YOUR_USERNAME and YOUR_REPO with actual values")
    print(f"=" * 60)
    
    for filename in sorted(os.listdir(images_dir)):
        if filename.endswith('.png'):
            url = f"{base_url}/{images_dir}/{filename}"
            episode_name = filename.replace('.png', '').replace('-', ' ').title()
            print(f"📸 {episode_name}")
            print(f"   {url}")
            print()
    
    print(f"📋 Instructions:")
    print(f"1. Create a new GitHub repository")
    print(f"2. Push this local repository to GitHub:")
    print(f"   git remote add origin https://github.com/YOUR_USERNAME/YOUR_REPO.git")
    print(f"   git branch -M main")
    print(f"   git push -u origin main")
    print(f"3. Update the URLs above with your actual GitHub username and repo name")

def main():
    """Main function"""
    print("🎨 Krvavý Dobšinský - Git Upload Script")
    print("=" * 50)
    
    # Create git structure and copy images
    images_dir, copied_count = create_git_repo_structure()
    
    if copied_count == 0:
        print("❌ No images found to upload!")
        return
    
    # Initialize git repository
    if initialize_git_repo(images_dir):
        # Generate URLs
        generate_urls(images_dir)
        
        print(f"\n🎉 Success! {copied_count} images ready for GitHub!")
        print(f"📁 Local directory: {os.path.abspath(images_dir)}")
    else:
        print("❌ Failed to initialize git repository")

if __name__ == "__main__":
    main()
