#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from openai import OpenAI
import requests

# Set your OpenAI API key
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

def generate_piargy_image():
    """Generate Piargy village image with custom prompt"""
    
    if not client.api_key:
        print("❌ Please set your OpenAI API key as environment variable:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        return
    
    # Custom prompt for Piargy
    prompt = """Stylized flat illustration in retro fairytale horror aesthetic, inspired by Eastern European folk tales.
The scene shows a remote mountain village (Piargy) under ominous clouds, with a church steeple in the center and shadowy figures standing motionless along crooked paths.
In the foreground, a solitary woman in a long dress walks toward the village, unaware of the silent menace around her.
Use simplified geometric shapes and a minimalist composition.
Color palette is limited to parchment beige (#D2B48C), blood red (#750000), dark brown (#2C1A0C), and small golden accents.
Background should have a soft paper texture.
No gradients, no photorealism – vector-style silhouettes only.
The image should feel mysterious, folkloric, symbolic, and slightly unsettling.
Consistent with podcast episode artwork.
NO TEXT OR LETTERS should appear anywhere in the image."""
    
    try:
        print("🎨 Generating Piargy village image...")
        print("📡 Sending request to OpenAI...")
        
        response = client.images.generate(
            prompt=prompt,
            n=1,
            size="1024x1024",
            model="dall-e-3"
        )
        
        print("📡 Response received!")
        
        image_url = response.data[0].url
        print(f"✅ Image generated successfully!")
        print(f"🔗 URL: {image_url}")
        
        # Download the image
        output_dir = os.path.expanduser("~/Desktop/Nové_Obrázky_Príbehy")
        os.makedirs(output_dir, exist_ok=True)
        
        image_response = requests.get(image_url)
        if image_response.status_code == 200:
            filepath = os.path.join(output_dir, "Piargy.png")
            with open(filepath, 'wb') as f:
                f.write(image_response.content)
            print(f"✅ Piargy image saved: {filepath}")
            return filepath
        else:
            print(f"❌ Failed to download Piargy image")
            return None
            
    except Exception as e:
        print(f"❌ Error generating Piargy image: {str(e)}")
        return None

if __name__ == "__main__":
    generate_piargy_image()
