#!/bin/bash

# K<PERSON>vý Dobšinský Project Setup Script
# This script sets up the entire project from scratch

set -e

echo "🔪 Setting up Krvavý Dobšinský Project..."
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Check system requirements
print_status "Checking system requirements..."

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ first."
    echo "Download from: https://nodejs.org/"
    exit 1
else
    NODE_VERSION=$(node --version)
    print_success "Node.js found: $NODE_VERSION"
fi

# Check npm
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
else
    NPM_VERSION=$(npm --version)
    print_success "npm found: $NPM_VERSION"
fi

# Check if Xcode is available (for iOS development)
if command -v xcodebuild &> /dev/null; then
    XCODE_VERSION=$(xcodebuild -version | head -n 1)
    print_success "Xcode found: $XCODE_VERSION"
else
    print_warning "Xcode not found. You'll need Xcode for iOS development."
    print_warning "Install from Mac App Store: https://apps.apple.com/app/xcode/id497799835"
fi

# Install dependencies
print_status "Installing project dependencies..."
npm install

if [ $? -eq 0 ]; then
    print_success "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Create necessary directories
print_status "Creating project directories..."

mkdir -p public/icons
mkdir -p public/screenshots
mkdir -p src/assets
mkdir -p src/utils
mkdir -p ios/App/App

print_success "Directories created"

# Generate app icons (placeholder)
print_status "Setting up app icons..."

# Create a simple SVG icon as placeholder
cat > public/icons/icon.svg << 'EOF'
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="512" height="512" rx="64" fill="#750000"/>
  <text x="256" y="320" font-family="Arial, sans-serif" font-size="200" fill="#D2B48C" text-anchor="middle">🔪</text>
</svg>
EOF

print_success "App icon template created"

# Set up environment variables template
print_status "Creating environment template..."

cat > .env.example << 'EOF'
# Krvavý Dobšinský App Environment Variables

# App Configuration
VITE_APP_NAME="Krvavý Dobšinský"
VITE_APP_VERSION="1.0.0"

# API URLs
VITE_RSS_FEED_URL="https://anchor.fm/s/8db2e1ec/podcast/rss"
VITE_GITHUB_IMAGES_BASE="https://raw.githubusercontent.com/vldseman/krvavy-dobsinsky-images/main/podcast_images/"

# Contact Information
VITE_SUPPORT_EMAIL="<EMAIL>"
VITE_INSTAGRAM_URL="https://www.instagram.com/krvavydobsinsky/"
VITE_HEROHERO_URL="https://herohero.co/krvavydobsinsky"

# Development
VITE_DEV_MODE=true
VITE_DEBUG_LOGS=true
EOF

# Copy to actual .env file if it doesn't exist
if [ ! -f ".env" ]; then
    cp .env.example .env
    print_success "Environment file created"
fi

# Set up Git hooks (if Git is available)
if command -v git &> /dev/null; then
    print_status "Setting up Git hooks..."
    
    # Create pre-commit hook
    mkdir -p .git/hooks
    
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# Pre-commit hook for Krvavý Dobšinský

echo "Running pre-commit checks..."

# Check if TypeScript compiles
npm run build > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ TypeScript compilation failed"
    exit 1
fi

echo "✅ Pre-commit checks passed"
EOF
    
    chmod +x .git/hooks/pre-commit
    print_success "Git hooks set up"
fi

# Build the project
print_status "Building the project..."
npm run build

if [ $? -eq 0 ]; then
    print_success "Project built successfully"
else
    print_error "Failed to build project"
    exit 1
fi

# Initialize Capacitor
print_status "Initializing Capacitor..."

# Check if Capacitor is already initialized
if [ ! -f "capacitor.config.ts" ]; then
    print_error "Capacitor config not found. This should have been created already."
    exit 1
fi

print_success "Capacitor configuration found"

# Make scripts executable
chmod +x scripts/*.sh

print_success "Project setup completed!"
echo ""
echo "================================================"
echo "🎉 Setup Complete!"
echo ""
echo "📱 Next Steps:"
echo "1. Run 'npm run dev' to start development server"
echo "2. Run 'npm run cap:add' to add iOS platform"
echo "3. Run 'npm run cap:build' to build for iOS"
echo ""
echo "🔧 Available Commands:"
echo "   npm run dev         - Start development server"
echo "   npm run build       - Build for production"
echo "   npm run preview     - Preview production build"
echo "   npm run cap:add     - Add iOS platform"
echo "   npm run cap:sync    - Sync with native platforms"
echo "   npm run cap:build   - Build for iOS"
echo "   npm run cap:open    - Open in Xcode"
echo ""
echo "📚 Documentation:"
echo "   - Capacitor: https://capacitorjs.com/docs"
echo "   - Vue 3: https://vuejs.org/guide/"
echo "   - Pinia: https://pinia.vuejs.org/"
echo ""
echo "🆘 Support:"
echo "   - Email: <EMAIL>"
echo "   - Instagram: @krvavydobsinsky"
echo "================================================"
