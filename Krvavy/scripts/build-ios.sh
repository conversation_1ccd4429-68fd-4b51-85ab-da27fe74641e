#!/bin/bash

# <PERSON><PERSON><PERSON><PERSON> Dobšinský iOS Build Script
# This script builds the app for iOS using Capacitor

set -e

echo "🔪 Building Krvavý Dobšinský iOS App..."
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    npm install
    print_success "Dependencies installed"
fi

# Build the web app
print_status "Building web application..."
npm run build

if [ $? -eq 0 ]; then
    print_success "Web application built successfully"
else
    print_error "Failed to build web application"
    exit 1
fi

# Check if Capacitor CLI is available
if ! command -v npx cap &> /dev/null; then
    print_error "Capacitor CLI not found. Installing..."
    npm install -g @capacitor/cli
fi

# Add iOS platform if it doesn't exist
if [ ! -d "ios" ]; then
    print_status "Adding iOS platform..."
    npx cap add ios
    print_success "iOS platform added"
fi

# Sync the web app with iOS
print_status "Syncing web app with iOS..."
npx cap sync ios

if [ $? -eq 0 ]; then
    print_success "Sync completed successfully"
else
    print_error "Failed to sync with iOS"
    exit 1
fi

# Copy custom iOS configuration
print_status "Copying iOS configuration..."

# Create iOS directories if they don't exist
mkdir -p ios/App/App

# Copy Info.plist if it exists
if [ -f "ios/App/App/Info.plist" ]; then
    print_success "Info.plist already exists"
else
    print_warning "Info.plist not found, using default"
fi

# Check if Xcode is installed
if ! command -v xcodebuild &> /dev/null; then
    print_warning "Xcode is not installed. You'll need Xcode to build the iOS app."
    print_status "Opening iOS project in Finder..."
    open ios/App
else
    print_status "Opening iOS project in Xcode..."
    npx cap open ios
fi

print_success "iOS build preparation completed!"
echo ""
echo "================================================"
echo "🎉 Next Steps:"
echo "1. Open the project in Xcode"
echo "2. Select your development team"
echo "3. Choose a device or simulator"
echo "4. Build and run the app"
echo ""
echo "📱 For App Store distribution:"
echo "1. Archive the app in Xcode"
echo "2. Upload to App Store Connect"
echo "3. Submit for review"
echo ""
echo "🔗 Useful commands:"
echo "   npm run cap:sync    - Sync changes"
echo "   npm run cap:open    - Open in Xcode"
echo "   npm run cap:run     - Build and run"
echo "================================================"
