<!DOCTYPE html>
<html lang="sk">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
  <meta name="theme-color" content="#2C1A0C" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  <meta name="apple-mobile-web-app-title" content="Krvavý Dobšinský" />
  
  <!-- App Icons -->
  <link rel="icon" type="image/png" sizes="32x32" href="/icons/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/icons/favicon-16x16.png">
  <link rel="apple-touch-icon" sizes="180x180" href="/icons/apple-touch-icon.png">
  <link rel="mask-icon" href="/icons/safari-pinned-tab.svg" color="#750000">
  
  <!-- PWA Manifest -->
  <link rel="manifest" href="/manifest.json">
  
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://anchor.fm">
  <link rel="preconnect" href="https://d3t3ozftmdmh3i.cloudfront.net">
  <link rel="preconnect" href="https://raw.githubusercontent.com">
  
  <title>Krvavý Dobšinský - Horror Podcast</title>
  
  <style>
    /* Loading screen styles */
    #loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #2C1A0C 0%, #1a0f06 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      color: #D2B48C;
    }
    
    .loading-logo {
      width: 120px;
      height: 120px;
      margin-bottom: 20px;
      border-radius: 20px;
      background: #750000;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 48px;
      font-weight: bold;
      color: #D2B48C;
      box-shadow: 0 8px 32px rgba(117, 0, 0, 0.3);
    }
    
    .loading-text {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 10px;
      text-align: center;
    }
    
    .loading-subtitle {
      font-size: 14px;
      opacity: 0.7;
      text-align: center;
      margin-bottom: 30px;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(210, 180, 140, 0.3);
      border-top: 3px solid #750000;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #2C1A0C;
      color: #D2B48C;
      overflow-x: hidden;
    }
    
    #app {
      min-height: 100vh;
    }
  </style>
</head>
<body>
  <div id="loading-screen">
    <div class="loading-logo">🔪</div>
    <div class="loading-text">Krvavý Dobšinský</div>
    <div class="loading-subtitle">Načítava sa...</div>
    <div class="loading-spinner"></div>
  </div>

  <div id="app">
    <!-- Fallback content if Vue doesn't load -->
    <div style="padding: 20px; text-align: center; color: #D2B48C; background: #2C1A0C; min-height: 100vh;">
      <h1>🔪 Krvavý Dobšinský</h1>
      <p>Aplikácia sa načítava...</p>
      <div id="debug-info"></div>
    </div>
  </div>

  <script>
    // Debug script to hide loading screen
    console.log('🔪 HTML loaded, hiding loading screen...');

    // Hide loading screen immediately
    setTimeout(() => {
      const loadingScreen = document.getElementById('loading-screen');
      if (loadingScreen) {
        loadingScreen.style.display = 'none';
        console.log('✅ Loading screen hidden');
      }

      // Show simple app content
      const app = document.getElementById('app');
      if (app) {
        app.innerHTML = `
          <div style="padding: 20px; text-align: center; color: #D2B48C; background: #2C1A0C; min-height: 100vh; font-family: -apple-system, BlinkMacSystemFont, sans-serif;">
            <div style="margin-bottom: 30px;">
              <div style="width: 80px; height: 80px; background: #750000; border-radius: 16px; display: inline-flex; align-items: center; justify-content: center; font-size: 40px; margin-bottom: 20px;">🔪</div>
              <h1 style="margin: 0; font-size: 28px; color: #D2B48C;">Krvavý Dobšinský</h1>
              <p style="margin: 10px 0; color: #DAA520; opacity: 0.8;">Horror Podcast</p>
            </div>

            <div style="max-width: 400px; margin: 0 auto;">
              <div style="background: rgba(44, 26, 12, 0.6); border: 1px solid #3d2817; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                <h3 style="margin: 0 0 10px 0; color: #D2B48C;">📱 Aplikácia funguje!</h3>
                <p style="margin: 0; color: #D2B48C; opacity: 0.9; line-height: 1.5;">
                  Toto je testovacia verzia aplikácie. Vue.js komponenty sa načítavajú na pozadí.
                </p>
              </div>

              <div style="background: rgba(117, 0, 0, 0.2); border: 1px solid #750000; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                <h3 style="margin: 0 0 10px 0; color: #D2B48C;">🎧 Testové epizódy</h3>
                <div style="text-align: left;">
                  <div style="padding: 10px; margin: 5px 0; background: rgba(44, 26, 12, 0.4); border-radius: 8px; cursor: pointer;" onclick="alert('🔪 Epizóda sa prehrá!')">
                    <strong>Test Epizóda 1</strong><br>
                    <small style="color: #DAA520;">Testovacia epizóda • 10:00</small>
                  </div>
                  <div style="padding: 10px; margin: 5px 0; background: rgba(44, 26, 12, 0.4); border-radius: 8px; cursor: pointer;" onclick="alert('🔪 Epizóda sa prehrá!')">
                    <strong>Test Epizóda 2</strong><br>
                    <small style="color: #DAA520;">Testovacia epizóda • 15:00</small>
                  </div>
                </div>
              </div>

              <div style="display: flex; gap: 10px; justify-content: center;">
                <button style="background: #750000; color: #D2B48C; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: 600;" onclick="alert('🎧 Podcast funguje!')">
                  🏠 Podcast
                </button>
                <button style="background: #3d2817; color: #D2B48C; border: 1px solid #DAA520; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: 600;" onclick="alert('❤️ Uložené funguje!')">
                  ❤️ Uložené
                </button>
                <button style="background: #3d2817; color: #D2B48C; border: 1px solid #DAA520; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: 600;" onclick="alert('ℹ️ O podcaste funguje!')">
                  ℹ️ O podcaste
                </button>
              </div>
            </div>
          </div>
        `;
        console.log('✅ Simple app content loaded');
      }
    }, 100);
  </script>

  <script type="module" src="/src/main.ts"></script>
</body>
</html>
