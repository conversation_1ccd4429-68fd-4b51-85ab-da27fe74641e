<!DOCTYPE html>
<html lang="sk">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
  <meta name="theme-color" content="#2C1A0C" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  <meta name="apple-mobile-web-app-title" content="Krvavý Dobšinský" />

  <!-- Content Security Policy - Allow external resources -->
  <meta http-equiv="Content-Security-Policy" content="
    default-src 'self' capacitor: https: data: blob:;
    script-src 'self' 'unsafe-inline' 'unsafe-eval' capacitor: https:;
    style-src 'self' 'unsafe-inline' capacitor: https:;
    img-src 'self' data: blob: capacitor: https: http:;
    media-src 'self' data: blob: capacitor: https: http:;
    connect-src 'self' capacitor: https: http: ws: wss:;
    font-src 'self' data: capacitor: https:;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
  ">

  <!-- App Icons -->
  <link rel="icon" type="image/png" sizes="32x32" href="/icons/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/icons/favicon-16x16.png">
  <link rel="apple-touch-icon" sizes="180x180" href="/icons/apple-touch-icon.png">
  <link rel="mask-icon" href="/icons/safari-pinned-tab.svg" color="#750000">
  
  <!-- PWA Manifest -->
  <link rel="manifest" href="/manifest.json">
  
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://anchor.fm">
  <link rel="preconnect" href="https://d3t3ozftmdmh3i.cloudfront.net">
  <link rel="preconnect" href="https://raw.githubusercontent.com">
  
  <title>Krvavý Dobšinský - Horror Podcast</title>
  
  <style>
    /* Loading screen styles */
    #loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #2C1A0C 0%, #1a0f06 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      color: #D2B48C;
    }
    
    .loading-logo {
      width: 120px;
      height: 120px;
      margin-bottom: 20px;
      border-radius: 20px;
      background: #750000;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 48px;
      font-weight: bold;
      color: #D2B48C;
      box-shadow: 0 8px 32px rgba(117, 0, 0, 0.3);
    }
    
    .loading-text {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 10px;
      text-align: center;
    }
    
    .loading-subtitle {
      font-size: 14px;
      opacity: 0.7;
      text-align: center;
      margin-bottom: 30px;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(210, 180, 140, 0.3);
      border-top: 3px solid #750000;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #2C1A0C;
      color: #D2B48C;
      overflow-x: hidden;
    }
    
    #app {
      min-height: 100vh;
    }
  </style>
</head>
<body>
  <div id="loading-screen">
    <div class="loading-logo">🔪</div>
    <div class="loading-text">Krvavý Dobšinský</div>
    <div class="loading-subtitle">Načítava sa...</div>
    <div class="loading-spinner"></div>
  </div>

  <div id="app">
    <!-- Fallback content if Vue doesn't load -->
    <div id="fallback-content" style="display: none; padding: 20px; text-align: center; color: #D2B48C; background: #2C1A0C; min-height: 100vh;">
      <h1>🔪 Krvavý Dobšinský</h1>
      <p>Aplikácia sa načítava...</p>
      <div id="debug-info"></div>
    </div>
  </div>

  <script>
    // Global error handler
    window.addEventListener('error', function(e) {
      console.error('🚨 Global error:', e.error);
      showFallbackContent('JavaScript Error: ' + e.message);
    });

    window.addEventListener('unhandledrejection', function(e) {
      console.error('🚨 Unhandled promise rejection:', e.reason);
      showFallbackContent('Promise Error: ' + e.reason);
    });

    function showFallbackContent(errorMsg) {
      console.log('🔄 Showing fallback content due to:', errorMsg);

      // Hide loading screen
      const loadingScreen = document.getElementById('loading-screen');
      if (loadingScreen) {
        loadingScreen.style.display = 'none';
      }

      // Show fallback content
      const fallback = document.getElementById('fallback-content');
      if (fallback) {
        fallback.style.display = 'block';
        const debugInfo = document.getElementById('debug-info');
        if (debugInfo) {
          debugInfo.innerHTML = `<p style="color: #ff6b6b; font-size: 12px; margin-top: 20px;">Debug: ${errorMsg}</p>`;
        }
      }
    }

    // Set a timeout to show fallback if Vue doesn't load
    let vueLoadTimeout = setTimeout(() => {
      console.log('⏰ Vue.js loading timeout - showing fallback');
      showFallbackContent('Vue.js loading timeout');
    }, 5000);

    // This will be cleared by main.ts when Vue loads successfully
    window.clearVueTimeout = function() {
      if (vueLoadTimeout) {
        clearTimeout(vueLoadTimeout);
        vueLoadTimeout = null;
        console.log('✅ Vue timeout cleared - app loaded successfully');
      }
    };

    console.log('🔪 HTML loaded, waiting for Vue.js...');
  </script>

  <script type="module" src="/src/main.ts"></script>
</body>
</html>
