import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Episode from '../views/Episode.vue'
import Saved from '../views/Saved.vue'
import About from '../views/About.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '<PERSON><PERSON><PERSON><PERSON>'
    }
  },
  {
    path: '/episode/:id',
    name: 'Episode',
    component: Episode,
    meta: {
      title: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
    }
  },
  {
    path: '/saved',
    name: 'Saved',
    component: Saved,
    meta: {
      title: 'Uložené'
    }
  },
  {
    path: '/about',
    name: 'About',
    component: About,
    meta: {
      title: 'O podcaste'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Update document title
router.beforeEach((to, from, next) => {
  document.title = to.meta.title ? `${to.meta.title} - <PERSON><PERSON><PERSON><PERSON>` : '<PERSON><PERSON><PERSON><PERSON>'
  next()
})

export default router
