// Global type definitions for Krvavý Dobšinský App

export interface Episode {
  id: string
  title: string
  description: string
  audioUrl: string
  imageUrl: string
  pubDate: string
  duration: string
  guid: string
}

export interface PodcastStore {
  episodes: Episode[]
  currentEpisode: Episode | null
  isPlaying: boolean
  isLoading: boolean
  savedEpisodes: string[]
  downloadedEpisodes: string[]
  currentTime: number
  duration: number
  volume: number
}

export interface AudioPlayerState {
  isExpanded: boolean
  currentTime: number
  duration: number
  volume: number
  isLoading: boolean
  error: string | null
}

export interface RSSFeedItem {
  title: string[]
  description: string[]
  enclosure?: Array<{
    $: {
      url: string
      type: string
      length: string
    }
  }>
  pubDate: string[]
  guid: Array<{
    _?: string
    $?: any
  } | string>
  'itunes:duration'?: string[]
  'itunes:image'?: Array<{
    $: {
      href: string
    }
  }>
}

export interface RSSFeed {
  rss: {
    channel: Array<{
      title: string[]
      description: string[]
      link: string[]
      image?: Array<{
        url: string[]
        title: string[]
        link: string[]
      }>
      item: RSSFeedItem[]
    }>
  }
}

export interface AppConfig {
  rssUrl: string
  githubImagesBase: string
  appName: string
  appVersion: string
  supportEmail: string
  instagramUrl: string
  heroHeroUrl: string
}

export interface NavigationItem {
  name: string
  path: string
  icon: string
  label: string
  badge?: number
}

export interface CapacitorPlugins {
  SplashScreen: any
  StatusBar: any
  App: any
  Preferences: any
  Share: any
  Filesystem: any
  Device: any
  Network: any
}

// Utility types
export type PlaybackState = 'playing' | 'paused' | 'stopped' | 'loading' | 'error'

export type ThemeMode = 'light' | 'dark' | 'auto'

export type SortOrder = 'newest' | 'oldest' | 'alphabetical' | 'duration'

export interface SearchFilters {
  query: string
  sortOrder: SortOrder
  showSavedOnly: boolean
}

export interface DownloadProgress {
  episodeId: string
  progress: number
  status: 'downloading' | 'completed' | 'error' | 'paused'
}

export interface AppSettings {
  theme: ThemeMode
  autoPlay: boolean
  downloadQuality: 'high' | 'medium' | 'low'
  skipIntro: boolean
  skipOutro: boolean
  playbackSpeed: number
  sleepTimer: number | null
}

// Event types
export interface AudioEvents {
  play: () => void
  pause: () => void
  stop: () => void
  timeupdate: (currentTime: number) => void
  loadedmetadata: (duration: number) => void
  ended: () => void
  error: (error: Error) => void
}

export interface AppEvents {
  episodePlay: (episode: Episode) => void
  episodePause: () => void
  episodeStop: () => void
  episodeSave: (episodeId: string) => void
  episodeUnsave: (episodeId: string) => void
  episodeDownload: (episodeId: string) => void
  episodeShare: (episode: Episode) => void
}

// API Response types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface EpisodeAnalytics {
  episodeId: string
  playCount: number
  totalListenTime: number
  completionRate: number
  lastPlayed: string
}

export interface UserPreferences {
  savedEpisodes: string[]
  downloadedEpisodes: string[]
  playbackHistory: EpisodeAnalytics[]
  settings: AppSettings
  lastSyncDate: string
}

// Component Props types
export interface EpisodeCardProps {
  episode: Episode
  showSaveButton?: boolean
  showDownloadButton?: boolean
  compact?: boolean
}

export interface AudioPlayerProps {
  episode: Episode | null
  isPlaying: boolean
  currentTime: number
  duration: number
  volume: number
}

export interface BottomNavigationProps {
  currentRoute: string
  savedCount: number
}

// Error types
export class AppError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message)
    this.name = 'AppError'
  }
}

export class NetworkError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 'NETWORK_ERROR', details)
    this.name = 'NetworkError'
  }
}

export class AudioError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 'AUDIO_ERROR', details)
    this.name = 'AudioError'
  }
}

export class StorageError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 'STORAGE_ERROR', details)
    this.name = 'StorageError'
  }
}

// Constants
export const APP_CONSTANTS = {
  RSS_FEED_URL: 'https://anchor.fm/s/8db2e1ec/podcast/rss',
  GITHUB_IMAGES_BASE: 'https://raw.githubusercontent.com/vldseman/krvavy-dobsinsky-images/main/podcast_images/',
  APP_NAME: 'Krvavý Dobšinský',
  APP_VERSION: '1.0.0',
  SUPPORT_EMAIL: '<EMAIL>',
  INSTAGRAM_URL: 'https://www.instagram.com/krvavydobsinsky/',
  HEROHERO_URL: 'https://herohero.co/krvavydobsinsky',
  
  // Audio settings
  DEFAULT_VOLUME: 1,
  SEEK_STEP: 15, // seconds
  MAX_RETRIES: 3,
  
  // Storage keys
  STORAGE_KEYS: {
    SAVED_EPISODES: 'savedEpisodes',
    DOWNLOADED_EPISODES: 'downloadedEpisodes',
    USER_PREFERENCES: 'userPreferences',
    PLAYBACK_HISTORY: 'playbackHistory',
    APP_SETTINGS: 'appSettings'
  },
  
  // Colors
  COLORS: {
    PARCHMENT: '#D2B48C',
    BLOOD_RED: '#750000',
    DARK_BROWN: '#2C1A0C',
    GOLDEN: '#DAA520',
    DARK_GRAY: '#1a0f06',
    MEDIUM_BROWN: '#3d2817'
  }
} as const

export default APP_CONSTANTS
