/* Global Styles for Krvavý Do<PERSON>š<PERSON> App */

:root {
  /* Color Palette - Retro Horror Theme */
  --color-parchment: #D2B48C;
  --color-blood-red: #750000;
  --color-dark-brown: #2C1A0C;
  --color-golden: #DAA520;
  --color-black: #000000;
  --color-dark-gray: #1a0f06;
  --color-medium-brown: #3d2817;
  
  /* Typography */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
  
  /* Safe areas for iOS */
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-right: env(safe-area-inset-right);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
  --safe-area-inset-left: env(safe-area-inset-left);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--color-parchment);
  background: var(--color-dark-brown);
  overflow-x: hidden;
  
  /* iOS specific */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  
  /* Prevent zoom on iOS */
  touch-action: manipulation;
}

/* Safe area handling */
.safe-area-top {
  padding-top: var(--safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: var(--safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: var(--safe-area-inset-left);
}

.safe-area-right {
  padding-right: var(--safe-area-inset-right);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  color: var(--color-parchment);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
  margin-bottom: var(--spacing-md);
}

/* Links */
a {
  color: var(--color-golden);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--color-parchment);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.btn-primary {
  background: var(--color-blood-red);
  color: var(--color-parchment);
}

.btn-primary:hover {
  background: #8b0000;
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--color-medium-brown);
  color: var(--color-parchment);
  border: 1px solid var(--color-golden);
}

.btn-secondary:hover {
  background: var(--color-golden);
  color: var(--color-dark-brown);
}

/* Form elements */
input, textarea, select {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-medium-brown);
  border-radius: var(--radius-md);
  background: var(--color-dark-gray);
  color: var(--color-parchment);
  font-size: var(--font-size-base);
  transition: border-color 0.2s ease;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--color-golden);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-dark-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--color-medium-brown);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-golden);
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-full { border-radius: var(--radius-full); }

.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* iOS specific fixes */
@supports (-webkit-touch-callout: none) {
  .ios-fix {
    -webkit-appearance: none;
    -webkit-border-radius: 0;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  /* Already dark by default */
}

/* Responsive design */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  .btn {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-lg);
  }
}
