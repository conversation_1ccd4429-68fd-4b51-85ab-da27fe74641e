console.log('🔪 Starting Krvavý Do<PERSON>š<PERSON>ký app...')

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { Capacitor } from '@capacitor/core'
import { SplashScreen } from '@capacitor/splash-screen'
import { StatusBar, Style } from '@capacitor/status-bar'
import { App as CapacitorApp } from '@capacitor/app'

console.log('📦 Imports loaded successfully')

import App from './App.vue'
import router from './router'

import './style.css'

console.log('🎨 Styles and components loaded')

console.log('🏗 Creating Vue app...')
const app = createApp(App)
const pinia = createPinia()

console.log('🔌 Setting up plugins...')
app.use(pinia)
app.use(router)

console.log('✅ Vue app configured')

// Initialize Capacitor plugins
const initializeApp = async () => {
  if (Capacitor.isNativePlatform()) {
    // Configure status bar
    await StatusBar.setStyle({ style: Style.Dark })
    await StatusBar.setBackgroundColor({ color: '#2C1A0C' })
    
    // Handle app state changes
    CapacitorApp.addListener('appStateChange', ({ isActive }) => {
      console.log('App state changed. Is active?', isActive)
    })
    
    // Handle back button on Android
    CapacitorApp.addListener('backButton', ({ canGoBack }) => {
      if (!canGoBack) {
        CapacitorApp.exitApp()
      } else {
        window.history.back()
      }
    })
  }
  
  // Hide loading screen after a shorter delay
  const loadingScreen = document.getElementById('loading-screen')
  if (loadingScreen) {
    setTimeout(() => {
      loadingScreen.style.opacity = '0'
      loadingScreen.style.transition = 'opacity 0.5s ease-out'
      setTimeout(() => {
        loadingScreen.remove()
      }, 500)
    }, 500) // Reduced from 1500ms to 500ms
  }
  
  // Hide splash screen
  if (Capacitor.isNativePlatform()) {
    await SplashScreen.hide()
  }
}

console.log('🚀 Mounting Vue app to #app...')
try {
  app.mount('#app')
  console.log('✅ Vue app mounted successfully!')
} catch (error) {
  console.error('❌ Error mounting Vue app:', error)
}

console.log('🔧 Initializing Capacitor...')
initializeApp()
