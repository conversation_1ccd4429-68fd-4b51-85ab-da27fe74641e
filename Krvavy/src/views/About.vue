<template>
  <div class="about-container safe-area-top">
    <!-- Header -->
    <header class="header">
      <div class="header-content">
        <h1 class="page-title">ℹ️ O podcaste</h1>
      </div>
    </header>

    <!-- Content -->
    <main class="about-content">
      <!-- Podcast Info -->
      <section class="podcast-info">
        <div class="podcast-logo">
          <div class="logo">🔪</div>
        </div>
        
        <h2 class="podcast-title">Krvavý Dobšinský</h2>
        <p class="podcast-subtitle">Slovenský horror podcast</p>
        
        <div class="description">
          <p>
            <strong>Krvavý Dobšinský</strong> je týždenný podcast tých najpríšernejších príbehov, 
            povier a strašidiel. Tradičné slovenské hororové poviedky a príbehy, ktoré vás 
            prenesú do sveta temných tajomstiev a nevysvetliteľných udalostí.
          </p>
          
          <p>
            Každá epizóda prináša autentické slovenské horror príbehy, inšpirované 
            východoslovenským folklórom a tradičnými povesťami. Pripravte sa na 
            nezabudnuteľné zážitky plné napätia a hrôzy.
          </p>
        </div>
      </section>

      <!-- Stats -->
      <section class="stats-section">
        <h3 class="section-title">📊 Štatistiky</h3>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-number">{{ totalEpisodes }}</div>
            <div class="stat-label">Epizód</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ savedCount }}</div>
            <div class="stat-label">Uložených</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ totalListenTime }}</div>
            <div class="stat-label">Hodín obsahu</div>
          </div>
        </div>
      </section>

      <!-- Features -->
      <section class="features-section">
        <h3 class="section-title">✨ Funkcie aplikácie</h3>
        <div class="features-list">
          <div class="feature-item">
            <div class="feature-icon">🎧</div>
            <div class="feature-content">
              <h4 class="feature-title">Offline prehrávanie</h4>
              <p class="feature-description">Stiahnite si epizódy a počúvajte ich aj bez internetu</p>
            </div>
          </div>
          
          <div class="feature-item">
            <div class="feature-icon">❤️</div>
            <div class="feature-content">
              <h4 class="feature-title">Uložené epizódy</h4>
              <p class="feature-description">Označte si obľúbené epizódy pre rýchly prístup</p>
            </div>
          </div>
          
          <div class="feature-item">
            <div class="feature-icon">🔍</div>
            <div class="feature-content">
              <h4 class="feature-title">Vyhľadávanie</h4>
              <p class="feature-description">Nájdite konkrétne epizódy podľa názvu alebo obsahu</p>
            </div>
          </div>
          
          <div class="feature-item">
            <div class="feature-icon">📤</div>
            <div class="feature-content">
              <h4 class="feature-title">Zdieľanie</h4>
              <p class="feature-description">Zdieľajte svoje obľúbené epizódy s priateľmi</p>
            </div>
          </div>
        </div>
      </section>

      <!-- Contact -->
      <section class="contact-section">
        <h3 class="section-title">📧 Kontakt</h3>
        <div class="contact-info">
          <div class="contact-item">
            <strong>Email:</strong> 
            <a href="mailto:<EMAIL>"><EMAIL></a>
          </div>
          <div class="contact-item">
            <strong>Instagram:</strong> 
            <a href="https://www.instagram.com/krvavydobsinsky/" target="_blank">@krvavydobsinsky</a>
          </div>
          <div class="contact-item">
            <strong>RSS Feed:</strong> 
            <a href="https://anchor.fm/s/8db2e1ec/podcast/rss" target="_blank">Anchor RSS</a>
          </div>
        </div>
      </section>

      <!-- Support -->
      <section class="support-section">
        <h3 class="section-title">💝 Podpora</h3>
        <div class="support-content">
          <p>
            Ak sa vám podcast páči, môžete podporiť jeho tvorbu cez 
            <a href="https://herohero.co/krvavydobsinsky" target="_blank">HeroHero</a>.
            Vaša podpora pomáha vytvárať nové epizódy a zlepšovať kvalitu obsahu.
          </p>
          
          <a 
            href="https://herohero.co/krvavydobsinsky" 
            target="_blank" 
            class="btn btn-primary support-btn"
          >
            💝 Podporiť podcast
          </a>
        </div>
      </section>

      <!-- App Info -->
      <section class="app-info-section">
        <h3 class="section-title">📱 O aplikácii</h3>
        <div class="app-details">
          <div class="detail-item">
            <strong>Verzia:</strong> 1.0.0
          </div>
          <div class="detail-item">
            <strong>Platforma:</strong> iOS (Capacitor)
          </div>
          <div class="detail-item">
            <strong>Vývojár:</strong> Vladimír Seman
          </div>
          <div class="detail-item">
            <strong>Posledná aktualizácia:</strong> {{ new Date().toLocaleDateString('sk-SK') }}
          </div>
        </div>
      </section>
    </main>

    <!-- Bottom Navigation -->
    <BottomNavigation />
    
    <!-- Audio Player -->
    <AudioPlayer v-if="podcastStore.currentEpisode" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePodcastStore } from '../stores/podcast'
import BottomNavigation from '../components/BottomNavigation.vue'
import AudioPlayer from '../components/AudioPlayer.vue'

const podcastStore = usePodcastStore()

const totalEpisodes = computed(() => {
  return podcastStore.episodes.length
})

const savedCount = computed(() => {
  return podcastStore.savedEpisodes.length
})

const totalListenTime = computed(() => {
  // Estimate total listening time (assuming average 20 minutes per episode)
  const averageMinutes = 20
  const totalMinutes = totalEpisodes.value * averageMinutes
  return Math.round(totalMinutes / 60)
})
</script>

<style scoped>
.about-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-dark-brown) 0%, var(--color-dark-gray) 100%);
  padding-bottom: 140px; /* Space for bottom nav and player */
}

.header {
  padding: var(--spacing-md);
  background: rgba(44, 26, 12, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-medium-brown);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-parchment);
  margin: 0;
}

.about-content {
  padding: var(--spacing-md);
  max-width: 800px;
  margin: 0 auto;
}

.podcast-info {
  text-align: center;
  padding: var(--spacing-xl) 0;
  border-bottom: 1px solid var(--color-medium-brown);
  margin-bottom: var(--spacing-xl);
}

.podcast-logo {
  margin-bottom: var(--spacing-lg);
}

.logo {
  width: 100px;
  height: 100px;
  background: var(--color-blood-red);
  border-radius: var(--radius-xl);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: var(--color-parchment);
  box-shadow: var(--shadow-xl);
}

.podcast-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--color-parchment);
  margin: 0 0 var(--spacing-sm) 0;
}

.podcast-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-golden);
  margin: 0 0 var(--spacing-lg) 0;
}

.description p {
  color: var(--color-parchment);
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
  opacity: 0.9;
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-parchment);
  margin: 0 0 var(--spacing-lg) 0;
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--color-blood-red);
}

.stats-section {
  margin-bottom: var(--spacing-xl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
}

.stat-card {
  background: rgba(44, 26, 12, 0.6);
  border: 1px solid var(--color-medium-brown);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--color-golden);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-parchment);
  opacity: 0.8;
}

.features-section {
  margin-bottom: var(--spacing-xl);
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: rgba(44, 26, 12, 0.4);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-medium-brown);
}

.feature-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-blood-red);
  border-radius: var(--radius-md);
  flex-shrink: 0;
}

.feature-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-parchment);
  margin: 0 0 var(--spacing-xs) 0;
}

.feature-description {
  color: var(--color-parchment);
  opacity: 0.8;
  margin: 0;
  line-height: 1.5;
}

.contact-section, .support-section, .app-info-section {
  margin-bottom: var(--spacing-xl);
}

.contact-info, .app-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.contact-item, .detail-item {
  color: var(--color-parchment);
  opacity: 0.9;
}

.contact-item a {
  color: var(--color-golden);
  text-decoration: none;
}

.contact-item a:hover {
  text-decoration: underline;
}

.support-content {
  text-align: center;
}

.support-content p {
  color: var(--color-parchment);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
  opacity: 0.9;
}

.support-content a {
  color: var(--color-golden);
  text-decoration: none;
}

.support-content a:hover {
  text-decoration: underline;
}

.support-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

@media (max-width: 768px) {
  .page-title {
    font-size: var(--font-size-lg);
  }
  
  .podcast-title {
    font-size: var(--font-size-2xl);
  }
  
  .logo {
    width: 80px;
    height: 80px;
    font-size: 36px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .feature-item {
    flex-direction: column;
    text-align: center;
  }
  
  .feature-icon {
    align-self: center;
  }
}
</style>
