<template>
  <div class="saved-container safe-area-top">
    <!-- Header -->
    <header class="header">
      <div class="header-content">
        <h1 class="page-title">❤️ Uložené epizódy</h1>
        <div class="saved-count">{{ savedEpisodes.length }} epizód</div>
      </div>
    </header>

    <!-- Content -->
    <main class="saved-content">
      <!-- Empty State -->
      <div v-if="savedEpisodes.length === 0" class="empty-state">
        <div class="empty-icon">💔</div>
        <h2 class="empty-title">Žiadne uložené epizódy</h2>
        <p class="empty-description">
          Uložte si svoje obľúbené epizódy kliknutím na srdce pri epizóde.
        </p>
        <router-link to="/" class="btn btn-primary">
          🏠 Prejsť na podcast
        </router-link>
      </div>

      <!-- Saved Episodes -->
      <div v-else class="episodes-container">
        <div class="episodes-grid">
          <EpisodeCard 
            v-for="episode in savedEpisodes" 
            :key="episode.id"
            :episode="episode"
            @play="handlePlay"
            @save="handleSave"
          />
        </div>
      </div>
    </main>

    <!-- Bottom Navigation -->
    <BottomNavigation />
    
    <!-- Audio Player -->
    <AudioPlayer v-if="podcastStore.currentEpisode" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePodcastStore } from '../stores/podcast'
import EpisodeCard from '../components/EpisodeCard.vue'
import BottomNavigation from '../components/BottomNavigation.vue'
import AudioPlayer from '../components/AudioPlayer.vue'
import type { Episode } from '../stores/podcast'

const podcastStore = usePodcastStore()

const savedEpisodes = computed(() => {
  return podcastStore.savedEpisodesList
})

const handlePlay = (episode: Episode) => {
  podcastStore.playEpisode(episode)
}

const handleSave = (episodeId: string) => {
  podcastStore.toggleSaveEpisode(episodeId)
}
</script>

<style scoped>
.saved-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-dark-brown) 0%, var(--color-dark-gray) 100%);
  padding-bottom: 140px; /* Space for bottom nav and player */
}

.header {
  padding: var(--spacing-md);
  background: rgba(44, 26, 12, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-medium-brown);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-parchment);
  margin: 0;
}

.saved-count {
  font-size: var(--font-size-sm);
  color: var(--color-golden);
  background: var(--color-medium-brown);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
}

.saved-content {
  padding: var(--spacing-md);
  max-width: 1200px;
  margin: 0 auto;
}

.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.empty-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--color-parchment);
  margin: 0 0 var(--spacing-md) 0;
}

.empty-description {
  color: var(--color-parchment);
  opacity: 0.7;
  line-height: 1.6;
  margin-bottom: var(--spacing-xl);
}

.episodes-container {
  padding-top: var(--spacing-lg);
}

.episodes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

@media (max-width: 768px) {
  .episodes-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: flex-start;
  }
  
  .page-title {
    font-size: var(--font-size-lg);
  }
  
  .empty-icon {
    font-size: 48px;
  }
  
  .empty-title {
    font-size: var(--font-size-xl);
  }
}
</style>
