<template>
  <div class="home-container safe-area-top">
    <!-- Header -->
    <header class="header">
      <div class="header-content">
        <div class="logo-section">
          <div class="logo">🔪</div>
          <div class="title-section">
            <h1 class="app-title"><PERSON>rvavý Do<PERSON>šinský</h1>
            <p class="app-subtitle">Horror Podcast</p>
          </div>
        </div>
        <button class="search-btn" @click="toggleSearch">
          🔍
        </button>
      </div>
      
      <!-- Search Bar -->
      <div v-if="showSearch" class="search-container">
        <input 
          v-model="searchQuery" 
          type="text" 
          placeholder="Hľadať epizódy..."
          class="search-input"
          @input="handleSearch"
        />
      </div>
    </header>

    <!-- Loading State -->
    <div v-if="podcastStore.isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p><PERSON><PERSON><PERSON>ta<PERSON><PERSON><PERSON> sa epizódy...</p>
    </div>

    <!-- Episodes List -->
    <main v-else class="episodes-container">
      <div v-if="filteredEpisodes.length === 0" class="no-episodes">
        <p>Žiadne epizódy sa nenašli.</p>
      </div>
      
      <div v-else class="episodes-grid">
        <EpisodeCard 
          v-for="episode in filteredEpisodes" 
          :key="episode.id"
          :episode="episode"
          @play="handlePlay"
          @save="handleSave"
        />
      </div>
    </main>

    <!-- Bottom Navigation -->
    <BottomNavigation />
    
    <!-- Audio Player -->
    <AudioPlayer v-if="podcastStore.currentEpisode" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { usePodcastStore } from '../stores/podcast'
import EpisodeCard from '../components/EpisodeCard.vue'
import BottomNavigation from '../components/BottomNavigation.vue'
import AudioPlayer from '../components/AudioPlayer.vue'
import type { Episode } from '../stores/podcast'

const podcastStore = usePodcastStore()

// Search functionality
const showSearch = ref(false)
const searchQuery = ref('')

const filteredEpisodes = computed(() => {
  if (!searchQuery.value) {
    return podcastStore.episodes
  }
  
  const query = searchQuery.value.toLowerCase()
  return podcastStore.episodes.filter(episode => 
    episode.title.toLowerCase().includes(query) ||
    episode.description.toLowerCase().includes(query)
  )
})

const toggleSearch = () => {
  showSearch.value = !showSearch.value
  if (!showSearch.value) {
    searchQuery.value = ''
  }
}

const handleSearch = () => {
  // Search is reactive through computed property
}

const handlePlay = (episode: Episode) => {
  podcastStore.playEpisode(episode)
}

const handleSave = (episodeId: string) => {
  podcastStore.toggleSaveEpisode(episodeId)
}

onMounted(() => {
  if (podcastStore.episodes.length === 0) {
    podcastStore.loadRSSFeed()
  }
})
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-dark-brown) 0%, var(--color-dark-gray) 100%);
  padding-bottom: 140px; /* Space for bottom nav and player */
}

.header {
  padding: var(--spacing-md);
  background: rgba(44, 26, 12, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-medium-brown);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo {
  width: 50px;
  height: 50px;
  background: var(--color-blood-red);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  box-shadow: var(--shadow-lg);
}

.title-section {
  display: flex;
  flex-direction: column;
}

.app-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-parchment);
  margin: 0;
}

.app-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-golden);
  margin: 0;
  opacity: 0.8;
}

.search-btn {
  background: var(--color-medium-brown);
  border: 1px solid var(--color-golden);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
  color: var(--color-parchment);
  font-size: 18px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.search-btn:hover {
  background: var(--color-golden);
  color: var(--color-dark-brown);
}

.search-container {
  margin-top: var(--spacing-md);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.search-input {
  width: 100%;
  padding: var(--spacing-md);
  background: var(--color-dark-gray);
  border: 1px solid var(--color-medium-brown);
  border-radius: var(--radius-lg);
  color: var(--color-parchment);
  font-size: var(--font-size-base);
}

.search-input:focus {
  border-color: var(--color-golden);
  outline: none;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(210, 180, 140, 0.3);
  border-top: 3px solid var(--color-blood-red);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.episodes-container {
  padding: var(--spacing-md);
  max-width: 1200px;
  margin: 0 auto;
}

.no-episodes {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--color-parchment);
  opacity: 0.7;
}

.episodes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

@media (max-width: 768px) {
  .episodes-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .header-content {
    padding: 0;
  }
  
  .app-title {
    font-size: var(--font-size-lg);
  }
  
  .logo {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
}
</style>
