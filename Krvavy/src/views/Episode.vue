<template>
  <div class="episode-container safe-area-top" v-if="episode">
    <!-- Header -->
    <header class="header">
      <div class="header-content">
        <button class="back-btn" @click="goBack">
          ← Späť
        </button>
        <h1 class="page-title">Epizóda</h1>
        <button 
          class="save-btn"
          :class="{ 'saved': isSaved }"
          @click="toggleSave"
        >
          <span v-if="isSaved">❤️</span>
          <span v-else>🤍</span>
        </button>
      </div>
    </header>

    <!-- Episode Content -->
    <main class="episode-content">
      <!-- Episode Hero -->
      <section class="episode-hero">
        <div class="episode-artwork-container">
          <img 
            :src="episode.imageUrl" 
            :alt="episode.title"
            class="episode-artwork"
            @error="handleImageError"
          />
          <div class="play-overlay" @click="togglePlay">
            <div class="play-button">
              <span v-if="isCurrentEpisode && podcastStore.isPlaying">⏸️</span>
              <span v-else>▶️</span>
            </div>
          </div>
        </div>
        
        <div class="episode-info">
          <h2 class="episode-title">{{ episode.title }}</h2>
          <div class="episode-meta">
            <span class="episode-date">{{ formatDate(episode.pubDate) }}</span>
            <span class="episode-duration">{{ episode.duration }}</span>
          </div>
          <p class="podcast-name">Krvavý Dobšinský</p>
        </div>
      </section>

      <!-- Episode Description -->
      <section class="episode-description">
        <h3 class="section-title">📖 Popis epizódy</h3>
        <div class="description-content" v-html="episode.description"></div>
      </section>

      <!-- Episode Actions -->
      <section class="episode-actions">
        <button 
          class="btn btn-primary play-btn"
          @click="togglePlay"
        >
          <span v-if="isCurrentEpisode && podcastStore.isPlaying">⏸️ Pozastaviť</span>
          <span v-else>▶️ Prehrať epizódu</span>
        </button>
        
        <div class="action-buttons">
          <button 
            class="btn btn-secondary"
            @click="shareEpisode"
          >
            📤 Zdieľať
          </button>
          
          <button 
            class="btn btn-secondary"
            @click="downloadEpisode"
          >
            📥 Stiahnuť
          </button>
        </div>
      </section>

      <!-- Related Episodes -->
      <section class="related-episodes" v-if="relatedEpisodes.length > 0">
        <h3 class="section-title">🎧 Podobné epizódy</h3>
        <div class="related-grid">
          <div 
            v-for="relatedEpisode in relatedEpisodes" 
            :key="relatedEpisode.id"
            class="related-item"
            @click="goToEpisode(relatedEpisode.id)"
          >
            <img 
              :src="relatedEpisode.imageUrl" 
              :alt="relatedEpisode.title"
              class="related-image"
            />
            <div class="related-info">
              <h4 class="related-title">{{ relatedEpisode.title }}</h4>
              <span class="related-duration">{{ relatedEpisode.duration }}</span>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Bottom Navigation -->
    <BottomNavigation />
    
    <!-- Audio Player -->
    <AudioPlayer v-if="podcastStore.currentEpisode" />
  </div>

  <!-- Loading State -->
  <div v-else class="loading-container safe-area-top">
    <div class="loading-spinner"></div>
    <p>Načítava sa epizóda...</p>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { usePodcastStore } from '../stores/podcast'
import { Share } from '@capacitor/share'
import { Capacitor } from '@capacitor/core'
import BottomNavigation from '../components/BottomNavigation.vue'
import AudioPlayer from '../components/AudioPlayer.vue'
import type { Episode } from '../stores/podcast'

const route = useRoute()
const router = useRouter()
const podcastStore = usePodcastStore()

const episode = ref<Episode | null>(null)

const isCurrentEpisode = computed(() => {
  return episode.value && podcastStore.currentEpisode?.id === episode.value.id
})

const isSaved = computed(() => {
  return episode.value ? podcastStore.savedEpisodes.includes(episode.value.id) : false
})

const relatedEpisodes = computed(() => {
  if (!episode.value) return []
  
  // Get 3 random episodes excluding current one
  const otherEpisodes = podcastStore.episodes.filter(ep => ep.id !== episode.value!.id)
  const shuffled = [...otherEpisodes].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, 3)
})

const loadEpisode = () => {
  const episodeId = route.params.id as string
  const foundEpisode = podcastStore.getEpisodeById(episodeId)
  
  if (foundEpisode) {
    episode.value = foundEpisode
  } else {
    // Episode not found, redirect to home
    router.push('/')
  }
}

const goBack = () => {
  router.back()
}

const togglePlay = () => {
  if (!episode.value) return
  
  if (isCurrentEpisode.value && podcastStore.isPlaying) {
    podcastStore.pauseEpisode()
  } else {
    podcastStore.playEpisode(episode.value)
  }
}

const toggleSave = () => {
  if (episode.value) {
    podcastStore.toggleSaveEpisode(episode.value.id)
  }
}

const shareEpisode = async () => {
  if (!episode.value) return
  
  if (Capacitor.isNativePlatform()) {
    try {
      await Share.share({
        title: episode.value.title,
        text: `Vypočuj si túto epizódu: ${episode.value.title}`,
        url: episode.value.audioUrl,
        dialogTitle: 'Zdieľať epizódu'
      })
    } catch (error) {
      console.error('Error sharing episode:', error)
    }
  } else {
    // Web fallback
    if (navigator.share) {
      try {
        await navigator.share({
          title: episode.value.title,
          text: `Vypočuj si túto epizódu: ${episode.value.title}`,
          url: window.location.href
        })
      } catch (error) {
        console.error('Error sharing episode:', error)
      }
    }
  }
}

const downloadEpisode = () => {
  if (!episode.value) return
  
  // Open audio URL in new tab for download
  window.open(episode.value.audioUrl, '_blank')
}

const goToEpisode = (episodeId: string) => {
  router.push(`/episode/${episodeId}`)
}

const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('sk-SK', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  } catch {
    return dateString
  }
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjMkMxQTBDIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTUwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDAiIGZpbGw9IiNEMkI0OEMiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJjZW50cmFsIj7wn5SQPC90ZXh0Pgo8L3N2Zz4K'
}

// Watch for route changes
watch(() => route.params.id, () => {
  loadEpisode()
})

onMounted(() => {
  // Load episodes if not already loaded
  if (podcastStore.episodes.length === 0) {
    podcastStore.loadRSSFeed().then(() => {
      loadEpisode()
    })
  } else {
    loadEpisode()
  }
})
</script>

<style scoped>
.episode-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-dark-brown) 0%, var(--color-dark-gray) 100%);
  padding-bottom: 140px; /* Space for bottom nav and player */
}

.header {
  padding: var(--spacing-md);
  background: rgba(44, 26, 12, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-medium-brown);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-btn {
  background: var(--color-medium-brown);
  border: 1px solid var(--color-golden);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--color-parchment);
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: var(--color-golden);
  color: var(--color-dark-brown);
}

.page-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-parchment);
  margin: 0;
}

.save-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: transform 0.2s ease;
}

.save-btn:hover {
  transform: scale(1.2);
}

.save-btn.saved {
  animation: heartbeat 0.6s ease-in-out;
}

.episode-content {
  padding: var(--spacing-md);
  max-width: 800px;
  margin: 0 auto;
}

.episode-hero {
  text-align: center;
  padding: var(--spacing-xl) 0;
  border-bottom: 1px solid var(--color-medium-brown);
  margin-bottom: var(--spacing-xl);
}

.episode-artwork-container {
  position: relative;
  display: inline-block;
  margin-bottom: var(--spacing-lg);
}

.episode-artwork {
  width: 250px;
  height: 250px;
  border-radius: var(--radius-xl);
  object-fit: cover;
  box-shadow: var(--shadow-xl);
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
  border-radius: var(--radius-xl);
}

.episode-artwork-container:hover .play-overlay {
  opacity: 1;
}

.play-button {
  width: 80px;
  height: 80px;
  background: var(--color-blood-red);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: var(--color-parchment);
  transition: transform 0.2s ease;
}

.play-button:hover {
  transform: scale(1.1);
}

.episode-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--color-parchment);
  margin: 0 0 var(--spacing-md) 0;
  line-height: 1.3;
}

.episode-meta {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--color-golden);
}

.podcast-name {
  font-size: var(--font-size-base);
  color: var(--color-parchment);
  opacity: 0.7;
  margin: 0;
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-parchment);
  margin: 0 0 var(--spacing-lg) 0;
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--color-blood-red);
}

.episode-description {
  margin-bottom: var(--spacing-xl);
}

.description-content {
  color: var(--color-parchment);
  line-height: 1.6;
  opacity: 0.9;
}

.episode-actions {
  margin-bottom: var(--spacing-xl);
}

.play-btn {
  width: 100%;
  padding: var(--spacing-lg);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-md);
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.related-episodes {
  margin-bottom: var(--spacing-xl);
}

.related-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.related-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: rgba(44, 26, 12, 0.4);
  border: 1px solid var(--color-medium-brown);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.2s ease;
}

.related-item:hover {
  background: rgba(44, 26, 12, 0.6);
  transform: translateX(4px);
}

.related-image {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-md);
  object-fit: cover;
  flex-shrink: 0;
}

.related-info {
  flex: 1;
  min-width: 0;
}

.related-title {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--color-parchment);
  margin: 0 0 var(--spacing-xs) 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.related-duration {
  font-size: var(--font-size-sm);
  color: var(--color-golden);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  text-align: center;
  color: var(--color-parchment);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(210, 180, 140, 0.3);
  border-top: 3px solid var(--color-blood-red);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes heartbeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.3); }
  100% { transform: scale(1); }
}

@media (max-width: 768px) {
  .episode-artwork {
    width: 200px;
    height: 200px;
  }
  
  .play-button {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }
  
  .episode-title {
    font-size: var(--font-size-xl);
  }
  
  .episode-meta {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .action-buttons {
    grid-template-columns: 1fr;
  }
  
  .page-title {
    font-size: var(--font-size-lg);
  }
}
</style>
