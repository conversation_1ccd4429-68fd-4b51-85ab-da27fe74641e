import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'
import { parseStringPromise } from 'xml2js'
import { Preferences } from '@capacitor/preferences'

export interface Episode {
  id: string
  title: string
  description: string
  audioUrl: string
  imageUrl: string
  pubDate: string
  duration: string
  guid: string
}

export const usePodcastStore = defineStore('podcast', () => {
  // State
  const episodes = ref<Episode[]>([])
  const currentEpisode = ref<Episode | null>(null)
  const isPlaying = ref(false)
  const isLoading = ref(false)
  const savedEpisodes = ref<string[]>([])
  const downloadedEpisodes = ref<string[]>([])
  const currentTime = ref(0)
  const duration = ref(0)
  const volume = ref(1)
  
  // RSS Feed URL
  const RSS_FEED_URL = 'https://anchor.fm/s/8db2e1ec/podcast/rss'
  
  // GitHub Images Base URL
  const GITHUB_IMAGES_BASE = 'https://raw.githubusercontent.com/vldseman/krvavy-dobsinsky-images/main/podcast_images/'
  
  // Computed
  const savedEpisodesList = computed(() => {
    return episodes.value.filter(episode => savedEpisodes.value.includes(episode.id))
  })
  
  const progress = computed(() => {
    return duration.value > 0 ? (currentTime.value / duration.value) * 100 : 0
  })
  
  // Actions
  const loadRSSFeed = async () => {
    isLoading.value = true
    try {
      const response = await axios.get(RSS_FEED_URL)
      const result = await parseStringPromise(response.data)
      
      const items = result.rss.channel[0].item || []
      
      episodes.value = items.map((item: any, index: number) => {
        const title = item.title[0]
        const cleanTitle = title.replace(/[🔪💀👻🎭🌙⚡️🔥💯🎪🎨🎬#\[\]]/g, '').trim()
        const urlFriendlyTitle = cleanTitle
          .toLowerCase()
          .replace(/[^\w\s\-\(\)]/g, '')
          .replace(/\s+/g, '-')
          .replace(/--+/g, '-')
          .trim('-')
        
        return {
          id: item.guid?.[0]?._ || item.guid?.[0] || `episode-${index}`,
          title: cleanTitle,
          description: item.description?.[0] || '',
          audioUrl: item.enclosure?.[0]?.$.url || '',
          imageUrl: `${GITHUB_IMAGES_BASE}${urlFriendlyTitle}.png`,
          pubDate: item.pubDate?.[0] || '',
          duration: item['itunes:duration']?.[0] || '00:00:00',
          guid: item.guid?.[0]?._ || item.guid?.[0] || `episode-${index}`
        }
      })
      
      // Load saved episodes from storage
      await loadSavedEpisodes()
      
    } catch (error) {
      console.error('Error loading RSS feed:', error)
    } finally {
      isLoading.value = false
    }
  }
  
  const playEpisode = (episode: Episode) => {
    currentEpisode.value = episode
    isPlaying.value = true
  }
  
  const pauseEpisode = () => {
    isPlaying.value = false
  }
  
  const stopEpisode = () => {
    currentEpisode.value = null
    isPlaying.value = false
    currentTime.value = 0
  }
  
  const saveEpisode = async (episodeId: string) => {
    if (!savedEpisodes.value.includes(episodeId)) {
      savedEpisodes.value.push(episodeId)
      await saveSavedEpisodes()
    }
  }
  
  const unsaveEpisode = async (episodeId: string) => {
    const index = savedEpisodes.value.indexOf(episodeId)
    if (index > -1) {
      savedEpisodes.value.splice(index, 1)
      await saveSavedEpisodes()
    }
  }
  
  const toggleSaveEpisode = async (episodeId: string) => {
    if (savedEpisodes.value.includes(episodeId)) {
      await unsaveEpisode(episodeId)
    } else {
      await saveEpisode(episodeId)
    }
  }
  
  const loadSavedEpisodes = async () => {
    try {
      const { value } = await Preferences.get({ key: 'savedEpisodes' })
      if (value) {
        savedEpisodes.value = JSON.parse(value)
      }
    } catch (error) {
      console.error('Error loading saved episodes:', error)
    }
  }
  
  const saveSavedEpisodes = async () => {
    try {
      await Preferences.set({
        key: 'savedEpisodes',
        value: JSON.stringify(savedEpisodes.value)
      })
    } catch (error) {
      console.error('Error saving episodes:', error)
    }
  }
  
  const setCurrentTime = (time: number) => {
    currentTime.value = time
  }
  
  const setDuration = (dur: number) => {
    duration.value = dur
  }
  
  const setVolume = (vol: number) => {
    volume.value = Math.max(0, Math.min(1, vol))
  }
  
  const getEpisodeById = (id: string): Episode | undefined => {
    return episodes.value.find(episode => episode.id === id)
  }
  
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
  
  return {
    // State
    episodes,
    currentEpisode,
    isPlaying,
    isLoading,
    savedEpisodes,
    downloadedEpisodes,
    currentTime,
    duration,
    volume,
    
    // Computed
    savedEpisodesList,
    progress,
    
    // Actions
    loadRSSFeed,
    playEpisode,
    pauseEpisode,
    stopEpisode,
    saveEpisode,
    unsaveEpisode,
    toggleSaveEpisode,
    setCurrentTime,
    setDuration,
    setVolume,
    getEpisodeById,
    formatDuration
  }
})
