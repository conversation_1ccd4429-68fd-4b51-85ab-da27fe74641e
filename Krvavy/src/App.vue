<template>
  <div id="app" class="app-container">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { usePodcastStore } from './stores/podcast'

const podcastStore = usePodcastStore()

onMounted(async () => {
  // Initialize the app
  await podcastStore.loadRSSFeed()
})
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #2C1A0C 0%, #1a0f06 100%);
  color: #D2B48C;
}
</style>
