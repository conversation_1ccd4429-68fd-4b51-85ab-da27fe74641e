<template>
  <div class="audio-player safe-area-bottom" v-if="podcastStore.currentEpisode">
    <!-- Mini Player (collapsed) -->
    <div 
      v-if="!isExpanded" 
      class="mini-player"
      @click="toggleExpanded"
    >
      <div class="mini-content">
        <img 
          :src="podcastStore.currentEpisode.imageUrl" 
          :alt="podcastStore.currentEpisode.title"
          class="mini-image"
        />
        <div class="mini-info">
          <h4 class="mini-title">{{ podcastStore.currentEpisode.title }}</h4>
          <div class="mini-progress">
            <div 
              class="mini-progress-bar" 
              :style="{ width: `${podcastStore.progress}%` }"
            ></div>
          </div>
        </div>
        <button 
          class="mini-play-btn"
          @click.stop="togglePlayPause"
        >
          <span v-if="podcastStore.isPlaying">⏸️</span>
          <span v-else>▶️</span>
        </button>
      </div>
    </div>

    <!-- Full Player (expanded) -->
    <div v-else class="full-player">
      <!-- Header -->
      <div class="player-header">
        <button class="collapse-btn" @click="toggleExpanded">
          ⬇️
        </button>
        <h3 class="player-title">Práve hrá</h3>
        <button class="close-btn" @click="closePlayer">
          ✕
        </button>
      </div>

      <!-- Episode Info -->
      <div class="episode-info">
        <img 
          :src="podcastStore.currentEpisode.imageUrl" 
          :alt="podcastStore.currentEpisode.title"
          class="episode-artwork"
        />
        <h2 class="episode-title">{{ podcastStore.currentEpisode.title }}</h2>
        <p class="podcast-name">Krvavý Dobšinský</p>
      </div>

      <!-- Progress Bar -->
      <div class="progress-section">
        <div class="time-display">
          <span class="current-time">{{ formatTime(currentTime) }}</span>
          <span class="total-time">{{ formatTime(duration) }}</span>
        </div>
        <div class="progress-container" @click="seekTo">
          <div class="progress-track">
            <div 
              class="progress-fill" 
              :style="{ width: `${podcastStore.progress}%` }"
            ></div>
            <div 
              class="progress-thumb" 
              :style="{ left: `${podcastStore.progress}%` }"
            ></div>
          </div>
        </div>
      </div>

      <!-- Controls -->
      <div class="player-controls">
        <button class="control-btn" @click="seekBackward">
          ⏪
        </button>
        <button class="main-play-btn" @click="togglePlayPause">
          <span v-if="podcastStore.isPlaying">⏸️</span>
          <span v-else>▶️</span>
        </button>
        <button class="control-btn" @click="seekForward">
          ⏩
        </button>
      </div>

      <!-- Additional Controls -->
      <div class="additional-controls">
        <button 
          class="control-btn"
          :class="{ 'active': isSaved }"
          @click="toggleSave"
        >
          <span v-if="isSaved">❤️</span>
          <span v-else>🤍</span>
        </button>
        
        <div class="volume-control">
          <span class="volume-icon">🔊</span>
          <input 
            type="range" 
            min="0" 
            max="1" 
            step="0.1"
            :value="podcastStore.volume"
            @input="setVolume"
            class="volume-slider"
          />
        </div>
        
        <button class="control-btn" @click="shareEpisode">
          📤
        </button>
      </div>
    </div>

    <!-- Hidden Audio Element -->
    <audio 
      ref="audioElement"
      @loadedmetadata="onLoadedMetadata"
      @timeupdate="onTimeUpdate"
      @ended="onEnded"
      @play="onPlay"
      @pause="onPause"
      preload="metadata"
    ></audio>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { usePodcastStore } from '../stores/podcast'
import { Share } from '@capacitor/share'
import { Capacitor } from '@capacitor/core'

const podcastStore = usePodcastStore()

// Player state
const isExpanded = ref(false)
const audioElement = ref<HTMLAudioElement | null>(null)
const currentTime = ref(0)
const duration = ref(0)

// Computed
const isSaved = computed(() => {
  return podcastStore.currentEpisode ? 
    podcastStore.savedEpisodes.includes(podcastStore.currentEpisode.id) : 
    false
})

// Watch for episode changes
watch(() => podcastStore.currentEpisode, (newEpisode) => {
  if (newEpisode && audioElement.value) {
    audioElement.value.src = newEpisode.audioUrl
    audioElement.value.load()
  }
}, { immediate: true })

// Watch for play state changes
watch(() => podcastStore.isPlaying, (isPlaying) => {
  if (audioElement.value) {
    if (isPlaying) {
      audioElement.value.play()
    } else {
      audioElement.value.pause()
    }
  }
})

// Methods
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

const closePlayer = () => {
  podcastStore.stopEpisode()
}

const togglePlayPause = () => {
  if (podcastStore.isPlaying) {
    podcastStore.pauseEpisode()
  } else {
    podcastStore.playEpisode(podcastStore.currentEpisode!)
  }
}

const seekBackward = () => {
  if (audioElement.value) {
    audioElement.value.currentTime = Math.max(0, audioElement.value.currentTime - 15)
  }
}

const seekForward = () => {
  if (audioElement.value) {
    audioElement.value.currentTime = Math.min(duration.value, audioElement.value.currentTime + 15)
  }
}

const seekTo = (event: MouseEvent) => {
  if (audioElement.value && duration.value > 0) {
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    const percent = (event.clientX - rect.left) / rect.width
    audioElement.value.currentTime = percent * duration.value
  }
}

const setVolume = (event: Event) => {
  const volume = parseFloat((event.target as HTMLInputElement).value)
  podcastStore.setVolume(volume)
  if (audioElement.value) {
    audioElement.value.volume = volume
  }
}

const toggleSave = () => {
  if (podcastStore.currentEpisode) {
    podcastStore.toggleSaveEpisode(podcastStore.currentEpisode.id)
  }
}

const shareEpisode = async () => {
  if (!podcastStore.currentEpisode) return
  
  if (Capacitor.isNativePlatform()) {
    try {
      await Share.share({
        title: podcastStore.currentEpisode.title,
        text: `Vypočuj si túto epizódu: ${podcastStore.currentEpisode.title}`,
        url: podcastStore.currentEpisode.audioUrl,
        dialogTitle: 'Zdieľať epizódu'
      })
    } catch (error) {
      console.error('Error sharing episode:', error)
    }
  }
}

const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// Audio event handlers
const onLoadedMetadata = () => {
  if (audioElement.value) {
    duration.value = audioElement.value.duration
    podcastStore.setDuration(duration.value)
  }
}

const onTimeUpdate = () => {
  if (audioElement.value) {
    currentTime.value = audioElement.value.currentTime
    podcastStore.setCurrentTime(currentTime.value)
  }
}

const onEnded = () => {
  podcastStore.pauseEpisode()
  currentTime.value = 0
  podcastStore.setCurrentTime(0)
}

const onPlay = () => {
  // Sync with store if needed
}

const onPause = () => {
  // Sync with store if needed
}

onMounted(() => {
  if (audioElement.value) {
    audioElement.value.volume = podcastStore.volume
  }
})

onUnmounted(() => {
  if (audioElement.value) {
    audioElement.value.pause()
  }
})
</script>

<style scoped>
.audio-player {
  position: fixed;
  bottom: 70px; /* Above bottom navigation */
  left: 0;
  right: 0;
  background: rgba(44, 26, 12, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid var(--color-medium-brown);
  z-index: 1000;
}

/* Mini Player Styles */
.mini-player {
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.mini-player:hover {
  background: rgba(44, 26, 12, 1);
}

.mini-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.mini-image {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-md);
  object-fit: cover;
}

.mini-info {
  flex: 1;
  min-width: 0;
}

.mini-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--color-parchment);
  margin: 0 0 var(--spacing-xs) 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mini-progress {
  height: 3px;
  background: var(--color-medium-brown);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.mini-progress-bar {
  height: 100%;
  background: var(--color-blood-red);
  transition: width 0.1s ease;
}

.mini-play-btn {
  background: var(--color-blood-red);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: var(--color-parchment);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.mini-play-btn:hover {
  transform: scale(1.1);
}

/* Full Player Styles */
.full-player {
  padding: var(--spacing-lg);
  max-height: 80vh;
  overflow-y: auto;
}

.player-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.collapse-btn, .close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: var(--color-parchment);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: background-color 0.2s ease;
}

.collapse-btn:hover, .close-btn:hover {
  background: var(--color-medium-brown);
}

.player-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-parchment);
  margin: 0;
}

.episode-info {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.episode-artwork {
  width: 200px;
  height: 200px;
  border-radius: var(--radius-lg);
  object-fit: cover;
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-xl);
}

.episode-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-parchment);
  margin: 0 0 var(--spacing-sm) 0;
  line-height: 1.3;
}

.podcast-name {
  font-size: var(--font-size-base);
  color: var(--color-golden);
  margin: 0;
  opacity: 0.8;
}

.progress-section {
  margin-bottom: var(--spacing-xl);
}

.time-display {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--color-parchment);
}

.progress-container {
  cursor: pointer;
  padding: var(--spacing-sm) 0;
}

.progress-track {
  position: relative;
  height: 6px;
  background: var(--color-medium-brown);
  border-radius: var(--radius-sm);
}

.progress-fill {
  height: 100%;
  background: var(--color-blood-red);
  border-radius: var(--radius-sm);
  transition: width 0.1s ease;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  width: 16px;
  height: 16px;
  background: var(--color-blood-red);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: var(--shadow-md);
}

.player-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.control-btn {
  background: var(--color-medium-brown);
  border: 1px solid var(--color-golden);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: var(--color-parchment);
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: var(--color-golden);
  color: var(--color-dark-brown);
  transform: scale(1.1);
}

.control-btn.active {
  background: var(--color-blood-red);
  border-color: var(--color-blood-red);
}

.main-play-btn {
  background: var(--color-blood-red);
  border: none;
  border-radius: 50%;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: var(--color-parchment);
  cursor: pointer;
  transition: transform 0.2s ease;
  box-shadow: var(--shadow-lg);
}

.main-play-btn:hover {
  transform: scale(1.1);
}

.additional-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
  max-width: 150px;
  margin: 0 var(--spacing-md);
}

.volume-icon {
  font-size: 16px;
}

.volume-slider {
  flex: 1;
  height: 4px;
  background: var(--color-medium-brown);
  border-radius: var(--radius-sm);
  outline: none;
  -webkit-appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: var(--color-blood-red);
  border-radius: 50%;
  cursor: pointer;
}

@media (max-width: 768px) {
  .episode-artwork {
    width: 150px;
    height: 150px;
  }
  
  .episode-title {
    font-size: var(--font-size-lg);
  }
  
  .player-controls {
    gap: var(--spacing-lg);
  }
  
  .control-btn {
    width: 45px;
    height: 45px;
    font-size: 18px;
  }
  
  .main-play-btn {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }
}
</style>
