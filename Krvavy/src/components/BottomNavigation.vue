<template>
  <nav class="bottom-navigation safe-area-bottom">
    <router-link 
      to="/" 
      class="nav-item"
      :class="{ 'active': $route.name === 'Home' }"
    >
      <div class="nav-icon">🏠</div>
      <span class="nav-label">Podcast</span>
    </router-link>
    
    <router-link 
      to="/saved" 
      class="nav-item"
      :class="{ 'active': $route.name === 'Saved' }"
    >
      <div class="nav-icon">❤️</div>
      <span class="nav-label">Uložené</span>
      <div v-if="savedCount > 0" class="badge">{{ savedCount }}</div>
    </router-link>
    
    <router-link 
      to="/about" 
      class="nav-item"
      :class="{ 'active': $route.name === 'About' }"
    >
      <div class="nav-icon">ℹ️</div>
      <span class="nav-label">O podcaste</span>
    </router-link>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePodcastStore } from '../stores/podcast'

const podcastStore = usePodcastStore()

const savedCount = computed(() => {
  return podcastStore.savedEpisodes.length
})
</script>

<style scoped>
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(44, 26, 12, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid var(--color-medium-brown);
  display: flex;
  justify-content: space-around;
  padding: var(--spacing-sm) 0;
  z-index: 1000;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: var(--color-parchment);
  opacity: 0.6;
  transition: all 0.2s ease;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  position: relative;
  min-width: 60px;
}

.nav-item:hover {
  opacity: 0.8;
  background: rgba(210, 180, 140, 0.1);
}

.nav-item.active {
  opacity: 1;
  color: var(--color-golden);
}

.nav-item.active .nav-icon {
  transform: scale(1.2);
}

.nav-icon {
  font-size: 20px;
  margin-bottom: var(--spacing-xs);
  transition: transform 0.2s ease;
}

.nav-label {
  font-size: var(--font-size-xs);
  font-weight: 500;
  text-align: center;
}

.badge {
  position: absolute;
  top: 2px;
  right: 8px;
  background: var(--color-blood-red);
  color: var(--color-parchment);
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

@media (max-width: 768px) {
  .bottom-navigation {
    padding: var(--spacing-md) 0;
  }
  
  .nav-icon {
    font-size: 22px;
  }
  
  .nav-label {
    font-size: var(--font-size-sm);
  }
  
  .nav-item {
    min-width: 70px;
  }
}
</style>
