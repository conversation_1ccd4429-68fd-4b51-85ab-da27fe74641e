<template>
  <div class="episode-card" :class="{ 'playing': isCurrentEpisode }">
    <div class="episode-image-container">
      <img 
        :src="episode.imageUrl" 
        :alt="episode.title"
        class="episode-image"
        @error="handleImageError"
        loading="lazy"
      />
      <div class="play-overlay" @click="$emit('play', episode)">
        <div class="play-button">
          <span v-if="isCurrentEpisode && podcastStore.isPlaying">⏸️</span>
          <span v-else>▶️</span>
        </div>
      </div>
    </div>
    
    <div class="episode-content">
      <div class="episode-header">
        <h3 class="episode-title">{{ episode.title }}</h3>
        <button 
          class="save-button"
          :class="{ 'saved': isSaved }"
          @click="$emit('save', episode.id)"
        >
          <span v-if="isSaved">❤️</span>
          <span v-else>🤍</span>
        </button>
      </div>
      
      <div class="episode-meta">
        <span class="episode-date">{{ formatDate(episode.pubDate) }}</span>
        <span class="episode-duration">{{ episode.duration }}</span>
      </div>
      
      <p class="episode-description" v-html="truncatedDescription"></p>
      
      <div class="episode-actions">
        <button 
          class="btn btn-primary play-btn"
          @click="$emit('play', episode)"
        >
          <span v-if="isCurrentEpisode && podcastStore.isPlaying">⏸️ Pozastaviť</span>
          <span v-else>▶️ Prehrať</span>
        </button>
        
        <button 
          class="btn btn-secondary share-btn"
          @click="shareEpisode"
        >
          📤 Zdieľať
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePodcastStore } from '../stores/podcast'
import { Share } from '@capacitor/share'
import { Capacitor } from '@capacitor/core'
import type { Episode } from '../stores/podcast'

interface Props {
  episode: Episode
}

const props = defineProps<Props>()
const emit = defineEmits<{
  play: [episode: Episode]
  save: [episodeId: string]
}>()

const podcastStore = usePodcastStore()

const isCurrentEpisode = computed(() => {
  return podcastStore.currentEpisode?.id === props.episode.id
})

const isSaved = computed(() => {
  return podcastStore.savedEpisodes.includes(props.episode.id)
})

const truncatedDescription = computed(() => {
  const description = props.episode.description
  if (description.length <= 150) {
    return description
  }
  return description.substring(0, 150) + '...'
})

const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('sk-SK', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  } catch {
    return dateString
  }
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // Fallback to a default horror-themed image
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjMkMxQTBDIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTUwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDAiIGZpbGw9IiNEMkI0OEMiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJjZW50cmFsIj7wn5SQPC90ZXh0Pgo8L3N2Zz4K'
}

const shareEpisode = async () => {
  if (Capacitor.isNativePlatform()) {
    try {
      await Share.share({
        title: props.episode.title,
        text: `Vypočuj si túto epizódu: ${props.episode.title}`,
        url: props.episode.audioUrl,
        dialogTitle: 'Zdieľať epizódu'
      })
    } catch (error) {
      console.error('Error sharing episode:', error)
    }
  } else {
    // Fallback for web
    if (navigator.share) {
      try {
        await navigator.share({
          title: props.episode.title,
          text: `Vypočuj si túto epizódu: ${props.episode.title}`,
          url: window.location.href
        })
      } catch (error) {
        console.error('Error sharing episode:', error)
      }
    } else {
      // Copy to clipboard fallback
      navigator.clipboard.writeText(`${props.episode.title} - ${window.location.href}`)
    }
  }
}
</script>

<style scoped>
.episode-card {
  background: rgba(44, 26, 12, 0.8);
  border: 1px solid var(--color-medium-brown);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.episode-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-golden);
}

.episode-card.playing {
  border-color: var(--color-blood-red);
  box-shadow: 0 0 20px rgba(117, 0, 0, 0.3);
}

.episode-image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.episode-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.episode-card:hover .episode-image {
  transform: scale(1.05);
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.episode-image-container:hover .play-overlay {
  opacity: 1;
}

.play-button {
  width: 60px;
  height: 60px;
  background: var(--color-blood-red);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: var(--color-parchment);
  transition: transform 0.2s ease;
}

.play-button:hover {
  transform: scale(1.1);
}

.episode-content {
  padding: var(--spacing-md);
}

.episode-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
}

.episode-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-parchment);
  margin: 0;
  flex: 1;
  margin-right: var(--spacing-sm);
  line-height: 1.3;
}

.save-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: transform 0.2s ease;
}

.save-button:hover {
  transform: scale(1.2);
}

.save-button.saved {
  animation: heartbeat 0.6s ease-in-out;
}

@keyframes heartbeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.3); }
  100% { transform: scale(1); }
}

.episode-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-sm);
  color: var(--color-golden);
}

.episode-description {
  color: var(--color-parchment);
  opacity: 0.8;
  line-height: 1.5;
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-sm);
}

.episode-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.play-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.share-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  white-space: nowrap;
}

@media (max-width: 768px) {
  .episode-actions {
    flex-direction: column;
  }
  
  .share-btn {
    width: 100%;
  }
  
  .episode-title {
    font-size: var(--font-size-base);
  }
  
  .episode-image-container {
    height: 180px;
  }
}
</style>
